/**
 * 渐进式图片加载组件
 * 适用于中国网络环境，支持低带宽渐进式加载图片
 */
const ProgressiveImageLoader = {
    /**
     * 初始化
     * @param {Object} options - 配置选项
     */
    init(options = {}) {
        // 默认配置
        this.config = {
            selector: '.progressive-image',
            threshold: 0.1,
            rootMargin: '50px 0px',
            placeholderColor: '#f0f0f0',
            placeholderSize: '10%',
            ...options
        };
        
        // 初始化观察器
        this.initObserver();
        
        // 立即加载首屏图片
        this.loadInitialImages();
        
        return this;
    },
    
    /**
     * 初始化交叉观察器
     */
    initObserver() {
        if ('IntersectionObserver' in window) {
            this.observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        this.loadImage(entry.target);
                        this.observer.unobserve(entry.target);
                    }
                });
            }, {
                threshold: this.config.threshold,
                rootMargin: this.config.rootMargin
            });
            
            // 开始观察所有图片
            document.querySelectorAll(this.config.selector).forEach(img => {
                if (!img.classList.contains('loaded')) {
                    // 初始化占位符效果
                    this.initPlaceholder(img);
                    
                    // 如果不是数据URL，则进行观察
                    if (!img.src || !img.src.startsWith('data:')) {
                        this.observer.observe(img);
                    }
                }
            });
        } else {
            // 降级处理：对于不支持IntersectionObserver的浏览器
            this.loadAllImages();
        }
    },
    
    /**
     * 初始化占位符效果
     * @param {HTMLImageElement} img - 图片元素
     */
    initPlaceholder(img) {
        // 如果没有设置模糊占位图
        if (!img.src || img.src === '') {
            // 设置颜色占位符
            img.style.backgroundColor = this.config.placeholderColor;
            
            // 保留正确的宽高比
            if (img.hasAttribute('width') && img.hasAttribute('height')) {
                const parent = img.parentElement;
                if (parent) {
                    parent.style.position = 'relative';
                    parent.style.overflow = 'hidden';
                    
                    const ratio = (parseInt(img.getAttribute('height'), 10) / 
                                   parseInt(img.getAttribute('width'), 10)) * 100;
                    
                    img.style.position = 'absolute';
                    img.style.top = '0';
                    img.style.left = '0';
                    img.style.width = '100%';
                    img.style.height = '100%';
                    
                    // 添加占位符元素维持宽高比
                    const placeholder = document.createElement('div');
                    placeholder.style.paddingBottom = ratio + '%';
                    placeholder.className = 'image-placeholder-ratio';
                    parent.insertBefore(placeholder, img);
                }
            }
        }
        
        // 设置过渡效果
        img.style.transition = 'filter 0.5s ease, opacity 0.5s ease';
        img.style.filter = 'blur(10px)';
        img.style.opacity = '0.6';
        
        // 添加类标识此图片需处理
        img.classList.add('loading');
    },
    
    /**
     * 加载特定图片
     * @param {HTMLImageElement} img - 图片元素
     */
    loadImage(img) {
        // 如果已加载，则跳过
        if (img.classList.contains('loaded')) return;
        
        // 获取高清图URL
        const src = img.dataset.src || img.getAttribute('data-src');
        
        if (!src) return;
        
        // 创建新图像对象预加载
        const newImg = new Image();
        
        // 加载成功后替换
        newImg.onload = () => {
            // 设置图片源
            img.src = src;
            
            // 移除模糊效果
            setTimeout(() => {
                img.style.filter = 'blur(0)';
                img.style.opacity = '1';
                
                // 标记为已加载
                img.classList.remove('loading');
                img.classList.add('loaded');
                
                // 移除data-src属性，防止重复加载
                img.removeAttribute('data-src');
                
                // 触发自定义事件
                img.dispatchEvent(new CustomEvent('image:loaded'));
            }, 50);
        };
        
        // 加载失败处理
        newImg.onerror = () => {
            // 加载失败时设置为默认图
            img.src = 'assets/images/image-error.svg';
            img.style.filter = 'blur(0)';
            img.style.opacity = '0.8';
            
            // 触发自定义事件
            img.dispatchEvent(new CustomEvent('image:error'));
        };
        
        // 使用自定义头部避免某些CDN限制，特别是在中国区域加载外部图片
        if (newImg.fetchPriority) {
            newImg.fetchPriority = 'high';
        }
        
        // 开始加载
        newImg.src = src;
    },
    
    /**
     * 加载首屏图片
     */
    loadInitialImages() {
        const viewportHeight = window.innerHeight;
        
        // 查找所有前三屏可能出现的图片
        document.querySelectorAll(this.config.selector).forEach(img => {
            const rect = img.getBoundingClientRect();
            
            // 如果在首屏或接近首屏
            if (rect.top < viewportHeight * 2) {
                this.loadImage(img);
                
                // 如果使用了观察器，则移除对该图片的观察
                if (this.observer) {
                    this.observer.unobserve(img);
                }
            }
        });
    },
    
    /**
     * 降级方案：加载所有图片
     */
    loadAllImages() {
        document.querySelectorAll(this.config.selector).forEach(img => {
            this.loadImage(img);
        });
    }
};

// 自动初始化
document.addEventListener('DOMContentLoaded', () => {
    ProgressiveImageLoader.init();
    
    // 处理动态添加的图片
    const observer = new MutationObserver((mutations) => {
        let newImages = false;
        
        mutations.forEach(mutation => {
            if (mutation.type === 'childList') {
                mutation.addedNodes.forEach(node => {
                    // 检查添加的节点是否包含图片
                    if (node.nodeType === 1) { // ELEMENT_NODE
                        if (node.matches && node.matches(ProgressiveImageLoader.config.selector)) {
                            newImages = true;
                        } else if (node.querySelectorAll) {
                            const images = node.querySelectorAll(ProgressiveImageLoader.config.selector);
                            if (images.length > 0) newImages = true;
                        }
                    }
                });
            }
        });
        
        // 如果有新图片，重新初始化
        if (newImages) {
            ProgressiveImageLoader.initObserver();
        }
    });
    
    // 开始观察DOM变化
    observer.observe(document.body, {
        childList: true,
        subtree: true
    });
}); 