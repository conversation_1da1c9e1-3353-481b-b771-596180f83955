/**
 * 微信分享优化组件
 * 用于配置在微信环境中分享网页时的标题、描述、图片等信息
 */
const WechatShare = {
    /**
     * 初始化微信分享配置
     * @param {Object} options - 分享配置选项
     */
    init(options = {}) {
        // 默认配置
        this.config = {
            title: document.title,
            desc: document.querySelector('meta[name="description"]')?.getAttribute('content') || '',
            link: window.location.href,
            imgUrl: document.querySelector('link[rel="apple-touch-icon"]')?.getAttribute('href') || '',
            type: 'website',
            dataUrl: '',
            success: function() {},
            cancel: function() {},
            ...options
        };
        
        // 检查是否在微信环境中
        this.isWechat = /MicroMessenger/i.test(navigator.userAgent);
        
        if (this.isWechat) {
            // 添加微信JSSDK
            this.loadWechatJSSDK(() => {
                this.setupWechatConfig();
            });
        }
        
        return this;
    },
    
    /**
     * 加载微信JSSDK
     * @param {Function} callback - 加载完成回调
     */
    loadWechatJSSDK(callback) {
        if (typeof wx !== 'undefined') {
            callback();
            return;
        }
        
        // 检查是否已经加载过JSSDK
        if (document.querySelector('script[src*="jweixin"]')) {
            // 脚本已存在，等待加载完成
            const checkInterval = setInterval(() => {
                if (typeof wx !== 'undefined') {
                    clearInterval(checkInterval);
                    callback();
                }
            }, 100);
            return;
        }
        
        // 加载JSSDK
        const script = document.createElement('script');
        script.src = 'https://res.wx.qq.com/open/js/jweixin-1.6.0.js';
        script.async = true;
        script.onload = callback;
        document.body.appendChild(script);
    },
    
    /**
     * 设置微信JS-SDK配置
     */
    setupWechatConfig() {
        // 从后端获取配置数据
        // 注意：实际应用中需要从服务器获取签名等信息
        this.getWechatConfig()
            .then(config => {
                wx.config({
                    debug: false,
                    appId: config.appId,
                    timestamp: config.timestamp,
                    nonceStr: config.nonceStr,
                    signature: config.signature,
                    jsApiList: [
                        'updateAppMessageShareData', 
                        'updateTimelineShareData',
                        'onMenuShareTimeline',
                        'onMenuShareAppMessage',
                        'onMenuShareQQ',
                        'onMenuShareWeibo'
                    ]
                });
                
                wx.ready(() => {
                    this.setShareInfo();
                    
                    // 触发自定义事件
                    document.dispatchEvent(new CustomEvent('wechat:ready'));
                });
                
                wx.error(res => {
                    console.error('微信JS-SDK配置失败:', res);
                });
            })
            .catch(error => {
                console.error('获取微信配置失败:', error);
                
                // 降级处理：使用简单的分享数据
                this.setupSimpleShare();
            });
    },
    
    /**
     * 获取微信JS-SDK配置
     * @returns {Promise} 配置数据Promise
     */
    getWechatConfig() {
        // 演示模式：使用模拟数据
        // 实际应用中应该通过API请求获取
        
        // 模拟从服务器获取配置
        if (this.demoMode) {
            return Promise.resolve({
                appId: 'wx_demo_app_id',
                timestamp: String(Math.floor(Date.now() / 1000)),
                nonceStr: this.generateNonceStr(),
                signature: 'demo_signature'
            });
        }
        
        // 实际场景：从服务器获取配置
        // const url = '/api/wechat/jsconfig?url=' + encodeURIComponent(window.location.href.split('#')[0]);
        // return fetch(url).then(res => res.json());
        
        // 临时方案：使用模拟数据
        return Promise.resolve({
            appId: 'wx_app_id',
            timestamp: String(Math.floor(Date.now() / 1000)),
            nonceStr: this.generateNonceStr(),
            signature: 'demo_signature'
        });
    },
    
    /**
     * 生成随机字符串
     * @returns {string} 随机字符串
     */
    generateNonceStr() {
        return Math.random().toString(36).substr(2, 15);
    },
    
    /**
     * 设置分享信息
     */
    setShareInfo() {
        const shareData = {
            title: this.config.title,
            desc: this.config.desc,
            link: this.config.link,
            imgUrl: this.config.imgUrl,
            type: this.config.type,
            dataUrl: this.config.dataUrl,
            success: this.config.success,
            cancel: this.config.cancel
        };
        
        // 设置新版分享接口（1.4.0+）
        if (wx.updateAppMessageShareData) {
            wx.updateAppMessageShareData(shareData);
        }
        
        if (wx.updateTimelineShareData) {
            wx.updateTimelineShareData(shareData);
        }
        
        // 兼容旧版接口
        if (wx.onMenuShareAppMessage) {
            wx.onMenuShareAppMessage(shareData);
        }
        
        if (wx.onMenuShareTimeline) {
            wx.onMenuShareTimeline(shareData);
        }
        
        if (wx.onMenuShareQQ) {
            wx.onMenuShareQQ(shareData);
        }
        
        if (wx.onMenuShareWeibo) {
            wx.onMenuShareWeibo(shareData);
        }
    },
    
    /**
     * 降级方案：简单分享
     */
    setupSimpleShare() {
        // 添加常见社交媒体的元标签
        this.addSocialMetaTags();
    },
    
    /**
     * 添加社交媒体元标签
     */
    addSocialMetaTags() {
        // 添加Open Graph标签（适用于微信、QQ等）
        this.addMetaTag('property', 'og:title', this.config.title);
        this.addMetaTag('property', 'og:description', this.config.desc);
        this.addMetaTag('property', 'og:image', this.config.imgUrl);
        this.addMetaTag('property', 'og:url', this.config.link);
        this.addMetaTag('property', 'og:type', 'website');
    },
    
    /**
     * 添加元标签
     * @param {string} attrName - 属性名
     * @param {string} attrValue - 属性值
     * @param {string} content - 内容值
     */
    addMetaTag(attrName, attrValue, content) {
        // 检查标签是否已存在
        let meta = document.querySelector(`meta[${attrName}="${attrValue}"]`);
        
        if (!meta) {
            meta = document.createElement('meta');
            meta.setAttribute(attrName, attrValue);
            document.head.appendChild(meta);
        }
        
        meta.setAttribute('content', content);
    },
    
    /**
     * 更新分享配置
     * @param {Object} options - 新的分享配置
     */
    updateConfig(options = {}) {
        this.config = {
            ...this.config,
            ...options
        };
        
        if (this.isWechat && typeof wx !== 'undefined') {
            this.setShareInfo();
        }
        
        // 更新元标签
        this.addSocialMetaTags();
    }
};

// 自动初始化（页面完全加载后）
window.addEventListener('load', () => {
    // 预设标题和描述
    const title = document.title;
    const desc = document.querySelector('meta[name="description"]')?.getAttribute('content') || '';
    
    // 查找合适的图片作为分享图
    const ogImage = document.querySelector('meta[property="og:image"]')?.getAttribute('content');
    const appleTouchIcon = document.querySelector('link[rel="apple-touch-icon"]')?.getAttribute('href');
    const favicon = document.querySelector('link[rel="icon"]')?.getAttribute('href');
    
    // 按优先级选择图片
    const imgUrl = ogImage || appleTouchIcon || favicon || '';
    
    // 初始化
    WechatShare.init({
        title: title,
        desc: desc,
        imgUrl: imgUrl,
        link: window.location.href
    });
    
    // 暴露给全局
    window.WechatShare = WechatShare;
}); 