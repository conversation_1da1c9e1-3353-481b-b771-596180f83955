/**
 * 网络检测与网络环境自适应优化组件
 * 特别针对中国网络环境下的弱网体验优化
 */
const NetworkDetector = {
    /**
     * 初始化网络检测
     * @param {Object} options - 配置选项
     */
    init(options = {}) {
        // 默认配置
        this.config = {
            // 检测间隔 (ms)
            checkInterval: 10000,
            // 视为慢速网络的RTT阈值 (ms)
            slowRttThreshold: 600,
            // 低带宽阈值 (Mbps)
            lowBandwidthThreshold: 1,
            // 慢速网络下是否自动降级
            autoDowngrade: true,
            // 网络变化回调
            onNetworkChange: null,
            // 启用网络诊断
            enableDiagnosis: true,
            ...options
        };
        
        // 网络状态
        this.networkStatus = {
            type: 'unknown',
            rtt: 0,
            downlink: 0,
            isSlowConnection: false,
            lastChecked: Date.now(),
            online: navigator.onLine
        };
        
        // 初始化网络监听
        this.setupNetworkListeners();
        
        // 立即执行检测
        this.checkNetworkQuality();
        
        // 定时检测
        this.checkInterval = setInterval(() => {
            this.checkNetworkQuality();
        }, this.config.checkInterval);
        
        return this;
    },
    
    /**
     * 设置网络事件监听
     */
    setupNetworkListeners() {
        // 监听在线状态变化
        window.addEventListener('online', () => {
            this.networkStatus.online = true;
            this.checkNetworkQuality();
            this.notifyNetworkChange();
        });
        
        window.addEventListener('offline', () => {
            this.networkStatus.online = false;
            this.notifyNetworkChange();
            this.applyOfflineMode();
        });
        
        // 监听网络信息变化（仅支持的浏览器）
        if ('connection' in navigator) {
            const connection = navigator.connection || 
                             navigator.mozConnection || 
                             navigator.webkitConnection;
            
            if (connection) {
                connection.addEventListener('change', () => {
                    this.updateNetworkInfo(connection);
                    this.notifyNetworkChange();
                    this.applyNetworkOptimizations();
                });
                
                // 初始化网络信息
                this.updateNetworkInfo(connection);
            }
        }
    },
    
    /**
     * 更新网络信息数据
     * @param {NetworkInformation} connection - 网络信息对象
     */
    updateNetworkInfo(connection) {
        this.networkStatus.type = connection.effectiveType || connection.type || 'unknown';
        this.networkStatus.rtt = connection.rtt || 0;
        this.networkStatus.downlink = connection.downlink || 0;
        this.networkStatus.lastChecked = Date.now();
        
        // 根据RTT和下行速度判断是否为慢速连接
        this.networkStatus.isSlowConnection = (
            this.networkStatus.rtt > this.config.slowRttThreshold ||
            this.networkStatus.downlink < this.config.lowBandwidthThreshold ||
            this.networkStatus.type === 'slow-2g' ||
            this.networkStatus.type === '2g'
        );
    },
    
    /**
     * 主动检测网络质量
     */
    checkNetworkQuality() {
        // 如果浏览器支持NetworkInformation API，优先使用
        if ('connection' in navigator) {
            const connection = navigator.connection || 
                             navigator.mozConnection || 
                             navigator.webkitConnection;
            
            if (connection) {
                this.updateNetworkInfo(connection);
                this.applyNetworkOptimizations();
                return;
            }
        }
        
        // 如果不支持NetworkInformation API，使用图片探测法
        this.probeNetworkSpeed();
    },
    
    /**
     * 使用图片探测网络速度
     */
    probeNetworkSpeed() {
        const startTime = Date.now();
        const probeImage = new Image();
        
        // 使用1x1像素透明GIF，体积小，适合测速
        // 这里应当换成网站自己的探针图片
        const probeUrl = 'data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7';
        
        probeImage.onload = () => {
            const endTime = Date.now();
            const duration = endTime - startTime;
            
            // 估算RTT
            this.networkStatus.rtt = duration;
            
            // 简单判断连接速度
            this.networkStatus.isSlowConnection = duration > 300; // 简单阈值
            
            // 应用网络优化
            this.applyNetworkOptimizations();
        };
        
        probeImage.onerror = () => {
            // 加载失败，可能是网络问题
            this.networkStatus.isSlowConnection = true;
            this.applyNetworkOptimizations();
        };
        
        // 强制不缓存
        probeImage.src = probeUrl + '?nocache=' + Math.random();
    },
    
    /**
     * 根据网络状况应用优化策略
     */
    applyNetworkOptimizations() {
        if (!this.config.autoDowngrade) return;
        
        if (this.networkStatus.isSlowConnection) {
            document.body.classList.add('slow-connection');
            
            // 应用慢速网络优化
            this.applySlowNetworkOptimizations();
        } else {
            document.body.classList.remove('slow-connection');
            
            // 恢复正常网络优化
            this.restoreNormalNetworkSettings();
        }
    },
    
    /**
     * 应用慢速网络的优化策略
     */
    applySlowNetworkOptimizations() {
        // 降低图片质量
        document.querySelectorAll('img:not(.essential)').forEach(img => {
            // 保存原始src以便恢复
            if (!img.dataset.originalSrc && img.src) {
                img.dataset.originalSrc = img.src;
            }
            
            // 如果有低质量版本，则替换
            if (img.dataset.lowQualitySrc) {
                img.src = img.dataset.lowQualitySrc;
            }
            
            // 添加模糊效果，提示用户这是低质量图像
            img.classList.add('low-quality');
        });
        
        // 禁用不必要的动画
        document.body.classList.add('disable-animations');
        
        // 减少背景图尺寸
        document.querySelectorAll('.bg-image, .hero, .banner').forEach(el => {
            if (!el.dataset.originalBg && el.style.backgroundImage) {
                el.dataset.originalBg = el.style.backgroundImage;
            }
            
            if (el.dataset.lowBandwidthBg) {
                el.style.backgroundImage = `url('${el.dataset.lowBandwidthBg}')`;
            }
        });
        
        // 显示慢速网络提示
        this.showSlowNetworkBanner();
    },
    
    /**
     * 恢复正常网络设置
     */
    restoreNormalNetworkSettings() {
        // 恢复原始图片
        document.querySelectorAll('img[data-original-src]').forEach(img => {
            img.src = img.dataset.originalSrc;
            img.classList.remove('low-quality');
        });
        
        // 恢复动画
        document.body.classList.remove('disable-animations');
        
        // 恢复背景图
        document.querySelectorAll('[data-original-bg]').forEach(el => {
            el.style.backgroundImage = el.dataset.originalBg;
        });
        
        // 隐藏慢速网络提示
        this.hideSlowNetworkBanner();
    },
    
    /**
     * 显示慢速网络提示横幅
     */
    showSlowNetworkBanner() {
        let banner = document.getElementById('slow-network-banner');
        
        if (!banner) {
            banner = document.createElement('div');
            banner.id = 'slow-network-banner';
            banner.className = 'slow-network-banner';
            banner.innerHTML = `
                <div class="slow-network-content">
                    <span class="slow-network-icon">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                            <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.42 0-8-3.58-8-8 0-1.85.63-3.55 1.69-4.9L16.9 18.31C15.55 19.37 13.85 20 12 20zm6.31-3.1L7.1 5.69C8.45 4.63 10.15 4 12 4c4.42 0 8 3.58 8 8 0 1.85-.63 3.55-1.69 4.9z" fill="currentColor"/>
                        </svg>
                    </span>
                    <span class="slow-network-message">检测到网络较慢，已为您优化浏览体验</span>
                    <button class="slow-network-close">&times;</button>
                </div>
            `;
            
            document.body.appendChild(banner);
            
            // 添加关闭功能
            banner.querySelector('.slow-network-close').addEventListener('click', () => {
                banner.classList.add('hidden');
            });
        } else {
            banner.classList.remove('hidden');
        }
    },
    
    /**
     * 隐藏慢速网络提示横幅
     */
    hideSlowNetworkBanner() {
        const banner = document.getElementById('slow-network-banner');
        if (banner) {
            banner.classList.add('hidden');
        }
    },
    
    /**
     * 应用离线模式
     */
    applyOfflineMode() {
        document.body.classList.add('offline-mode');
        
        // 显示离线提示
        this.showOfflineNotification();
        
        // 尝试从缓存加载资源
        this.loadFromCache();
    },
    
    /**
     * 显示离线通知
     */
    showOfflineNotification() {
        let notification = document.getElementById('offline-notification');
        
        if (!notification) {
            notification = document.createElement('div');
            notification.id = 'offline-notification';
            notification.className = 'offline-notification';
            notification.innerHTML = `
                <div class="offline-notification-content">
                    <span class="offline-icon">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                            <path d="M24 13h-2v-2h2v2zm-4.5 5c-.8 0-1.5-.7-1.5-1.5v-9c0-.8.7-1.5 1.5-1.5s1.5.7 1.5 1.5v9c0 .8-.7 1.5-1.5 1.5zM11 15V3c0-.6-.4-1-1-1s-1 .4-1 1v12H7.5c-.8 0-1.5.7-1.5 1.5S6.7 18 7.5 18h5c.8 0 1.5-.7 1.5-1.5s-.7-1.5-1.5-1.5H11zm-6.5 1c-.3 0-.5-.2-.5-.5v-1c0-.3.2-.5.5-.5s.5.2.5.5v1c0 .3-.2.5-.5.5zm0-3c-.3 0-.5-.2-.5-.5v-1c0-.3.2-.5.5-.5s.5.2.5.5v1c0 .3-.2.5-.5.5zm0-6c-.3 0-.5-.2-.5-.5v-1c0-.3.2-.5.5-.5s.5.2.5.5v1c0 .3-.2.5-.5.5zm0 3c-.3 0-.5-.2-.5-.5v-1c0-.3.2-.5.5-.5s.5.2.5.5v1c0 .3-.2.5-.5.5z" fill="currentColor"/>
                        </svg>
                    </span>
                    <span class="offline-message">您的网络已断开，正在使用离线缓存</span>
                </div>
            `;
            
            document.body.appendChild(notification);
        } else {
            notification.classList.remove('hidden');
        }
    },
    
    /**
     * 从缓存加载资源
     */
    loadFromCache() {
        // 如果有缓存管理器，尝试从缓存加载
        if (window.CacheManager) {
            // 这里可以实现具体的缓存加载策略
        }
    },
    
    /**
     * 通知网络变化
     */
    notifyNetworkChange() {
        // 触发网络变化事件
        document.dispatchEvent(new CustomEvent('network:change', {
            detail: this.networkStatus
        }));
        
        // 如果提供了回调，则调用
        if (typeof this.config.onNetworkChange === 'function') {
            this.config.onNetworkChange(this.networkStatus);
        }
    },
    
    /**
     * 获取当前网络状态
     * @returns {Object} 网络状态
     */
    getStatus() {
        return {...this.networkStatus};
    },
    
    /**
     * 诊断网络问题
     * @returns {Promise} 诊断结果
     */
    diagnoseNetwork() {
        if (!this.config.enableDiagnosis) {
            return Promise.resolve({
                message: '网络诊断未启用'
            });
        }
        
        return new Promise(resolve => {
            const results = {
                online: navigator.onLine,
                pingResults: [],
                dnsResults: [],
                suggestions: []
            };
            
            // 尝试Ping几个常用的中国CDN
            const cdnHosts = [
                'https://cdn.bootcdn.net/ajaxcn.org',  // BootCDN
                'https://s1.hdslb.com',                // B站CDN
                'https://gw.alipayobjects.com'         // 阿里CDN
            ];
            
            // 简单的Ping测试
            const pingTests = cdnHosts.map(host => {
                return this.pingHost(host).then(time => {
                    results.pingResults.push({host, time});
                    
                    if (time > 1000) {
                        results.suggestions.push(`访问${host}延迟较高 (${time}ms)，可能影响页面加载速度`);
                    }
                }).catch(err => {
                    results.pingResults.push({host, error: err.message});
                    results.suggestions.push(`无法访问${host}，请检查网络连接或DNS设置`);
                });
            });
            
            // 执行所有测试
            Promise.allSettled(pingTests).then(() => {
                // 分析结果
                if (results.pingResults.every(result => result.error)) {
                    results.suggestions.push('所有CDN测试均失败，请检查您的网络连接');
                } else {
                    const successes = results.pingResults.filter(result => !result.error);
                    const avgTime = successes.reduce((sum, {time}) => sum + time, 0) / successes.length;
                    
                    if (avgTime > 500) {
                        results.suggestions.push(`平均网络延迟 ${Math.round(avgTime)}ms，建议切换到更快的网络`);
                    }
                }
                
                resolve(results);
            });
        });
    },
    
    /**
     * Ping特定主机
     * @param {string} host - 主机URL
     * @returns {Promise} 响应时间
     */
    pingHost(host) {
        return new Promise((resolve, reject) => {
            const startTime = Date.now();
            const img = new Image();
            
            // 设置超时
            const timeout = setTimeout(() => {
                img.onload = img.onerror = null;
                reject(new Error('请求超时'));
            }, 3000);
            
            img.onload = function() {
                clearTimeout(timeout);
                resolve(Date.now() - startTime);
            };
            
            img.onerror = function() {
                clearTimeout(timeout);
                // 对于错误，我们仍然可以测量响应时间
                // 因为onerror被触发意味着服务器返回了响应
                resolve(Date.now() - startTime);
            };
            
            // 添加随机参数避免缓存
            img.src = `${host}/favicon.ico?nocache=${Math.random()}`;
        });
    }
};

// 自动初始化
document.addEventListener('DOMContentLoaded', () => {
    NetworkDetector.init();
    
    // 暴露给全局，便于其他脚本使用
    window.NetworkDetector = NetworkDetector;
    
    // 添加必要的样式
    const style = document.createElement('style');
    style.textContent = `
        .slow-network-banner {
            position: fixed;
            bottom: 20px;
            left: 20px;
            background-color: rgba(var(--primary-color-rgb, 26, 54, 93), 0.85);
            color: #fff;
            padding: 12px 16px;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            z-index: 1000;
            font-size: 14px;
            backdrop-filter: blur(4px);
            transition: opacity 0.3s ease, transform 0.3s ease;
            transform: translateY(0);
        }
        
        .slow-network-banner.hidden {
            opacity: 0;
            transform: translateY(20px);
            pointer-events: none;
        }
        
        .slow-network-content {
            display: flex;
            align-items: center;
        }
        
        .slow-network-icon {
            margin-right: 8px;
        }
        
        .slow-network-close {
            background: none;
            border: none;
            color: #fff;
            font-size: 18px;
            cursor: pointer;
            margin-left: 12px;
            padding: 0 4px;
        }
        
        .offline-notification {
            position: fixed;
            top: 20px;
            right: 20px;
            background-color: rgba(var(--tech-gray-rgb, 84, 106, 123), 0.9);
            color: #fff;
            padding: 12px 16px;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            z-index: 1000;
            font-size: 14px;
            backdrop-filter: blur(4px);
        }
        
        .offline-notification.hidden {
            display: none;
        }
        
        .offline-notification-content {
            display: flex;
            align-items: center;
        }
        
        .offline-icon {
            margin-right: 8px;
        }
        
        img.low-quality {
            filter: blur(2px);
            transition: filter 0.3s ease;
        }
        
        .disable-animations * {
            animation-duration: 0s !important;
            transition-duration: 0s !important;
        }
    `;
    document.head.appendChild(style);
}); 