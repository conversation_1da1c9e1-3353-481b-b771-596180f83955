<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="查看矿业信息化领域专家的成功案例，了解如何通过智能化解决方案实现煤矿安全高效生产">
    <title>成功案例研究 | 张洁贞的矿业信息化解决方案</title>
    
    <!-- 预加载关键资源 -->
    <link rel="preload" href="../assets/css/main.css" as="style">
    <link rel="preload" href="../assets/images/background-texture2.webp" as="image" type="image/webp">
    <link rel="preconnect" href="https://cdn.bootcdn.net" crossorigin>
    <link rel="preconnect" href="https://cdn.jsdelivr.net" crossorigin>
    
    <!-- 关键渲染路径CSS -->
    <style>
        /* 立即需要的最小CSS集合 */
        :root {
            --primary-color: #1a365d;
            --secondary-color: #4299e1;
            --dark-blue: #2d3748;
            --white: #ffffff;
            --light-gray: #f7fafc;
        }

        body {
            margin: 0;
            padding: 0;
            font-family: sans-serif;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        body.ready {
            opacity: 1;
        }

        .header {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            background-color: var(--white);
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            z-index: 1000;
        }

        .navbar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1rem;
            max-width: 1200px;
            margin: 0 auto;
        }

        .logo {
            text-decoration: none;
            color: var(--primary-color);
            font-weight: bold;
            font-size: 1.5rem;
        }

        .nav-menu {
            display: flex;
            list-style: none;
            margin: 0;
            padding: 0;
        }

        .page-title {
            margin-top: 80px;
            min-height: 60vh;
            background-color: var(--primary-color);
            color: var(--white);
            display: flex;
            align-items: center;
            justify-content: center;
            text-align: center;
            position: relative;
        }

        /* 基础样式重置 */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            line-height: 1.6;
            color: var(--dark-blue);
        }

        /* 导航栏样式优化 */
        .logo span {
            color: var(--secondary-color);
            margin-left: 4px;
        }

        .logo-subtitle {
            font-size: 0.875rem;
            color: var(--dark-blue);
            margin-left: 8px;
        }

        .nav-item {
            margin: 0 1rem;
        }

        .nav-link {
            text-decoration: none;
            color: var(--dark-blue);
            font-weight: 500;
            transition: color 0.3s ease;
        }

        .nav-link:hover,
        .nav-link.active {
            color: var(--secondary-color);
        }

        .nav-contact-btn {
            background-color: var(--primary-color);
            color: var(--white);
            padding: 0.5rem 1rem;
            border-radius: 4px;
            text-decoration: none;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            transition: background-color 0.3s ease;
        }

        .nav-contact-btn:hover {
            background-color: var(--secondary-color);
        }

        /* 移动导航样式 */
        .mobile-nav-toggle {
            display: none;
            background: none;
            border: none;
            font-size: 1.5rem;
            color: var(--dark-blue);
            cursor: pointer;
        }

        .mobile-nav-wrapper {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 1001;
        }

        .mobile-nav {
            position: fixed;
            top: 0;
            right: -100%;
            width: 80%;
            max-width: 300px;
            height: 100%;
            background-color: var(--white);
            padding: 2rem;
            transition: right 0.3s ease;
            z-index: 1002;
        }

        .mobile-nav.active {
            right: 0;
        }

        .mobile-nav-backdrop {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
            opacity: 0;
            visibility: hidden;
            transition: opacity 0.3s ease;
        }

        .mobile-nav-backdrop.active {
            opacity: 1;
            visibility: visible;
        }

        /* 页面内容样式 */
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 1rem;
        }

        /* 页面标题部分样式 */
        .page-title::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
        }

        .page-title .container {
            position: relative;
            z-index: 1;
        }

        .page-title h1 {
            font-size: 2.5rem;
            margin-bottom: 1rem;
        }

        .page-title p {
            font-size: 1.125rem;
            max-width: 600px;
            margin: 0 auto;
        }

        /* 首屏关键样式 */
        .page-header {
            min-height: 60vh;
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            background-color: #1a365d; /* 添加背景色作为图片加载前的占位 */
        }
        .page-header-bg {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-image: url('../assets/images/case-studies-bg.webp');
            background-size: cover;
            background-position: center;
            opacity: 0;
            transition: opacity 0.3s ease;
        }
        .page-header-bg.loaded {
            opacity: 1;
        }
        .page-header-content {
            position: relative;
            z-index: 1;
            text-align: center;
        }

        /* 确保navbar使用flex布局并垂直居中 */
        .navbar {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        /* 自定义NProgress样式 */
        #nprogress .bar {
            background: var(--primary-color); /* 使用网站主色调 */
            height: 3px;
        }
        #nprogress .peg {
            box-shadow: 0 0 10px var(--primary-color), 0 0 5px var(--primary-color);
        }
        #nprogress .spinner-icon {
            border-top-color: var(--primary-color);
            border-left-color: var(--primary-color);
        }

        /* Swiper客户评价轮播样式 */
        .testimonial-swiper {
            padding-bottom: 50px;
            /* 确保容器可见 */
            overflow: hidden; 
            position: relative;
            width: 100%;
            max-width: 800px; /* 根据需要调整最大宽度 */
            margin: 0 auto; /* 居中显示 */
        }
        .swiper-pagination-bullet {
            width: 10px;
            height: 10px;
            background-color: #ccc;
            opacity: 0.5;
        }
        .swiper-pagination-bullet-active {
            background-color: var(--primary-color);
            opacity: 1;
        }
        .swiper-button-next, .swiper-button-prev {
            color: var(--primary-color);
            /* 确保按钮在容器内可见 */
            top: 50%;
            transform: translateY(-50%);
             width: 30px; /* 调整大小 */
            height: 30px; /* 调整大小 */
            background-color: rgba(255, 255, 255, 0.7); /* 添加背景使其更明显 */
            border-radius: 50%;
            line-height: 30px; /* 垂直居中图标 */
            text-align: center; /* 水平居中图标 */
        }
         .swiper-button-next::after, .swiper-button-prev::after {
            font-size: 16px; /* 调整箭头大小 */
        }


        .testimonial {
            padding: 20px;
            transition: transform 0.3s;
            background-color: #f9f9f9; /* 添加背景色以区分 */
            border-radius: 8px; /* 添加圆角 */
            box-shadow: 0 2px 5px rgba(0,0,0,0.1); /* 添加轻微阴影 */
            text-align: center; /* 居中文本 */
            min-height: 250px; /* 确保有最小高度 */
            display: flex;
            flex-direction: column;
            justify-content: space-between;
        }
        .testimonial-content {
            margin-bottom: 20px;
        }
        .testimonial-author {
             display: flex;
            align-items: center;
            justify-content: center; /* 居中作者信息 */
        }
         .testimonial-author img {
            width: 50px; /* 调整头像大小 */
            height: 50px;
            border-radius: 50%;
            margin-right: 15px;
        }
        .author-info {
            text-align: left;
        }
         .author-info h4 {
            margin: 0 0 5px 0;
            font-size: 1rem;
            color: var(--dark-blue);
        }
        .author-info p {
            margin: 0;
            font-size: 0.9rem;
            color: #666;
        }

        /* 按钮悬停效果 */
        .btn {
            transition: all 0.3s ease-in-out;
            position: relative;
            overflow: hidden;
        }
        .btn:after {
            content: '';
            position: absolute;
            width: 0;
            height: 100%;
            top: 0;
            left: 0;
            background: rgba(255, 255, 255, 0.1);
            transition: width 0.3s ease;
        }
        .btn:hover:after {
            width: 100%;
        }
        .btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
        }

        /* 案例卡片悬停效果 */
        .case-card {
            transition: all 0.3s ease-in-out;
        }
        .case-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
        }

        /* 导航链接悬停效果 */
        .nav-link {
            position: relative;
            overflow: hidden;
        }
        .nav-link:before {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 0;
            height: 2px;
            background-color: var(--primary-color);
            transition: width 0.3s ease;
        }
        .nav-link:hover:before {
            width: 100%;
        }

        /* 阅读更多链接悬停效果 */
        .read-more {
            transition: all 0.3s ease;
        }
        .read-more:hover {
            color: var(--primary-color);
            padding-left: 5px;
        }
        .read-more i {
            transition: transform 0.3s ease;
        }
        .read-more:hover i {
            transform: translateX(5px);
        }
         /* 响应式调整 - 确保移动端导航切换按钮可见 */
        @media (max-width: 992px) {
            .mobile-nav-toggle {
                display: block; /* 确保在小屏幕上显示 */
                order: 3; /* 将按钮放在最右侧 */
            }
             .navbar {
                /* 确保logo和按钮在两端 */
                 justify-content: space-between;
            }
             .nav-menu {
                display: none; /* 隐藏桌面菜单 */
            }
            .logo {
                 flex-grow: 1; /* 让logo占据剩余空间，将按钮推到右边 */
            }
        }
        @media (min-width: 993px) {
             .mobile-nav-toggle {
                display: none; /* 在大屏幕上隐藏汉堡菜单 */
            }
        }

        /* 筛选器和搜索框样式 */
        .filter-wrapper {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
            margin-bottom: 40px;
            align-items: center;
        }

        .filter-group {
            flex: 1;
            min-width: 200px;
        }

        .filter-group label {
            display: block;
            margin-bottom: 8px;
            color: var(--dark-blue);
            font-weight: 500;
        }

        .filter-group select {
            width: 100%;
            padding: 10px;
            border: 1px solid #e2e8f0;
            border-radius: 6px;
            background-color: white;
            font-size: 1rem;
            color: var(--dark-blue);
        }

        .search-box {
            flex: 1;
            min-width: 250px;
            position: relative;
        }

        .search-box input {
            width: 100%;
            padding: 10px 40px 10px 15px;
            border: 1px solid #e2e8f0;
            border-radius: 6px;
            font-size: 1rem;
            color: var(--dark-blue);
        }

        .search-box button {
            position: absolute;
            right: 10px;
            top: 50%;
            transform: translateY(-50%);
            background: none;
            border: none;
            color: var(--dark-blue);
            cursor: pointer;
        }

        .search-box input::placeholder {
            color: #a0aec0;
        }

        .testimonials-section {
            background: var(--white);
        }
        
        .testimonial {
            background: var(--light-gray);
            padding: 30px;
            border-radius: 15px;
            margin: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.05);
            transition: transform 0.3s ease;
        }
        
        .testimonial:hover {
            transform: translateY(-5px);
        }
        
        .testimonial-content {
            margin-bottom: 20px;
        }
        
        .testimonial-content p {
            font-size: 1.1rem;
            line-height: 1.6;
            color: var(--dark-blue);
            font-style: italic;
        }
        
        .testimonial-author {
            display: flex;
            align-items: center;
            gap: 15px;
        }
        
        .testimonial-author img {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            object-fit: cover;
        }
        
        .author-info h4 {
            margin: 0;
            color: var(--primary-color);
            font-size: 1.1rem;
        }
        
        .author-info p {
            margin: 5px 0 0;
            color: var(--dark-blue);
            font-size: 0.9rem;
            opacity: 0.8;
        }
        
        /* Swiper导航按钮样式 */
        .swiper-button-next,
        .swiper-button-prev {
            color: var(--primary-color);
            background: var(--white);
            width: 40px;
            height: 40px;
            border-radius: 50%;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .swiper-button-next:after,
        .swiper-button-prev:after {
            font-size: 18px;
        }
        
        .swiper-pagination-bullet-active {
            background: var(--primary-color);
        }

    </style>

    <!-- 非阻塞加载主CSS -->
    <link rel="stylesheet" href="../assets/css/main.css" media="print" onload="this.media='all'; if(document.body) document.body.classList.add('ready')">
    <noscript>
        <link rel="stylesheet" href="../assets/css/main.css">
    </noscript>

    <!-- 预加载字体 -->
    <link rel="preload" as="font" href="../assets/fonts/source-serif-pro-600.woff2" type="font/woff2" crossorigin>
    
    <!-- 添加Font Awesome图标库 -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@5.15.4/css/all.min.css">
    
    <script>
        // 确保页面样式加载完成后再显示内容
        document.addEventListener('DOMContentLoaded', function() {
            if (document.querySelector('link[href="../assets/css/main.css"]').sheet && document.body) {
                document.body.classList.add('ready');
            }
        });
    </script>

    
    <!-- NProgress加载进度条 -->
    <link rel="stylesheet" href="https://cdn.bootcdn.net/ajax/libs/nprogress/0.2.0/nprogress.min.css">
    <script src="https://cdn.bootcdn.net/ajax/libs/nprogress/0.2.0/nprogress.min.js"></script>
    <!-- Swiper轮播库 -->
    <link rel="stylesheet" href="https://cdn.bootcdn.net/ajax/libs/Swiper/8.4.5/swiper-bundle.min.css">
    <!-- 添加AOS动画库 -->
    <link rel="stylesheet" href="https://cdn.bootcdn.net/ajax/libs/aos/2.3.4/aos.css">
    <script src="https://cdn.bootcdn.net/ajax/libs/aos/2.3.4/aos.js"></script>
    <!-- 移除或注释掉 fix-mobile.css，因为它可能引起冲突 -->
    <!-- <link rel="stylesheet" href="fix-mobile.css"> -->
    <script src="https://cdn.bootcdn.net/ajax/libs/Swiper/8.4.5/swiper-bundle.min.js" defer></script>
</head>
<body class="ready">
    <!-- 顶部导航栏 -->
    <header class="header">
        <div class="container">
            <nav class="navbar">
                <a href="../index.html" class="logo">张<span>洁贞</span><span class="logo-subtitle">矿业信息化专家</span></a>
                <ul class="nav-menu">
                    <li class="nav-item"><a href="../index.html" class="nav-link">首页</a></li>
                    <li class="nav-item"><a href="../about.html" class="nav-link">关于我</a></li>
                    <li class="nav-item"><a href="../blog/index.html" class="nav-link">洞见</a></li>
                    <li class="nav-item"><a href="../case-studies/index.html" class="nav-link active">案例</a></li>
                    <li class="nav-item"><a href="../contact.html" class="nav-link">联系</a></li>
                    <li class="nav-item"><a href="../contact.html" class="nav-contact-btn"><i class="fas fa-headset"></i>咨询我</a></li>
                </ul>
                <button class="mobile-nav-toggle">
                    <i class="fas fa-bars"></i>
                </button>
            </nav>
        </div>
    </header>

    <!-- 移动导航菜单 -->
    <div class="mobile-nav-wrapper">
        <div class="mobile-nav">
            <button class="close-mobile-nav">
                <i class="fas fa-times"></i>
            </button>
            <ul>
                <li><a href="../index.html">首页</a></li>
                <li><a href="../about.html">关于我</a></li>
                <li><a href="../blog/index.html">洞见</a></li>
                <li><a href="../case-studies/index.html" class="active">案例</a></li>
                <li><a href="../contact.html">联系</a></li>
            </ul>
        </div>
        <div class="mobile-nav-backdrop"></div>
    </div>
    
    <!-- 页面标题部分 -->
    <section style="position: relative; background: url('../assets/images/background-texture2.webp') center/cover; padding: 120px 0 80px;">
        <div style="position: absolute; top: 0; left: 0; right: 0; bottom: 0; background: linear-gradient(135deg, rgba(26, 54, 93, 0.45), rgba(21, 32, 50, 0.65));"></div>
        <div class="container" style="position: relative; z-index: 2;">
            <h1 style="color: var(--white); margin-bottom: 1rem; font-size: 2.8rem; text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);" data-aos="fade-up" data-aos-delay="100">成功案例研究</h1>
            <p style="font-size: 1.2rem; max-width: 700px; color: rgba(255, 255, 255, 0.95); text-shadow: 0 1px 3px rgba(0, 0, 0, 0.2); line-height: 1.6;" data-aos="fade-up" data-aos-delay="200">深入了解我如何帮助煤矿企业实现安全高效生产，通过智能化技术解决矿山生产挑战</p>
        </div>
    </section>

    <!-- 案例研究列表 -->
    <section class="case-studies-list">
        <div class="container">
            <!-- 特色案例 -->
            <div class="featured-case" data-aos="fade-up" data-aos-duration="1000">
                <div class="featured-case-image">
                    <img src="../assets/images/case-featured.webp" alt="特色案例：陕西某大型煤矿智能化系统升级">
                    <div class="featured-badge">特色案例</div>
                </div>
                <div class="featured-case-content">
                    <div class="case-category">智能矿山 · 井工煤矿</div>
                    <h2>陕西某大型煤矿集团智能化系统升级</h2>
                    <p>面对安全生产压力与效率提升需求，我为该煤矿集团设计了覆盖安全监测、生产调度、智能开采和通风优化的综合解决方案。该项目实现了安全事故率显著下降和生产效率大幅提升，成为省级智能化示范矿井。</p>
                    <div class="case-stats">
                        <div class="stat">
                            <span class="stat-value">58%</span>
                            <span class="stat-label">安全事故率下降</span>
                        </div>
                        <div class="stat">
                            <span class="stat-value">42%</span>
                            <span class="stat-label">生产效率提升</span>
                        </div>
                        <div class="stat">
                            <span class="stat-value">8个月</span>
                            <span class="stat-label">项目周期</span>
                        </div>
                    </div>
                    <a href="case-tech-team.html" class="btn btn-primary">查看详情</a>
                </div>
            </div>

            <!-- 案例研究网格 -->
            <div class="case-grid">
                <!-- 案例1 -->
                <div class="case-card" data-industry="coal" data-challenge="safety" data-aos="fade-up" data-aos-delay="100">
                    <div class="case-image">
                        <img src="../assets/images/case1.webp" alt="山西煤矿安全监测系统案例">
                    </div>
                    <div class="case-content">
                        <div class="case-category">安全监测 · 井工煤矿</div>
                        <h3>山西某集团多矿井安全生产监管平台建设</h3>
                        <p>为山西省大型煤炭集团构建统一的安全生产监管平台，整合15家矿井的安全监测数据，实现瓦斯、顶板、水害等风险的智能预警，安全事故率下降45%。</p>
                        <a href="case-bank-rebranding.html" class="read-more">查看案例 <i class="fas fa-arrow-right"></i></a>
                    </div>
                </div>

                <!-- 案例2 -->
                <div class="case-card" data-industry="coal" data-challenge="intelligent-mining" data-aos="fade-up" data-aos-delay="200">
                    <div class="case-image">
                        <img src="../assets/images/case2.webp" alt="智能开采工作面建设">
                    </div>
                    <div class="case-content">
                        <div class="case-category">智能开采 · 井工煤矿</div>
                        <h3>陕西榆林智能化综采工作面建设</h3>
                        <p>为陕西榆林某矿井设计智能化综采工作面解决方案，实现采煤机、液压支架、刮板输送机的协同控制，工作面80%时间实现无人作业，单产提高35%。</p>
                        <a href="case-retail-growth.html" class="read-more">查看案例 <i class="fas fa-arrow-right"></i></a>
                    </div>
                </div>

                <!-- 案例3 -->
                <div class="case-card" data-industry="metal" data-challenge="digital-twin" data-aos="fade-up" data-aos-delay="300">
                    <div class="case-image">
                        <img src="../assets/images/case3.webp" alt="矿山数字孪生平台建设">
                    </div>
                    <div class="case-content">
                        <div class="case-category">数字孪生 · 露天矿山</div>
                        <h3>某大型露天矿山数字孪生平台构建</h3>
                        <p>为大型露天矿山建设全面的数字孪生平台，实现矿山地质、采场、设备、运输等全要素的三维可视化和动态监测，决策效率提升62%，运营成本降低18%。</p>
                        <a href="case-medical-entry.html" class="read-more">查看案例 <i class="fas fa-arrow-right"></i></a>
                    </div>
                </div>

                <!-- 案例4 -->
                <div class="case-card" data-industry="coal" data-challenge="ventilation" data-aos="fade-up" data-aos-delay="100">
                    <div class="case-image">
                        <img src="../assets/images/case4.webp" alt="矿井通风系统优化">
                    </div>
                    <div class="case-content">
                        <div class="case-category">通风优化 · 井工煤矿</div>
                        <h3>山西某复杂构造矿井通风系统智能优化</h3>
                        <p>针对山西某复杂地质构造矿井的通风难题，设计实施智能通风系统，实现通风网络的动态监测与优化调控，通风效率提升32%，能耗降低25%。</p>
                        <a href="case-manufacturing-crisis.html" class="read-more">查看案例 <i class="fas fa-arrow-right"></i></a>
                    </div>
                </div>

                <!-- 案例5 -->
                <div class="case-card" data-industry="coal" data-challenge="production" data-aos="fade-up" data-aos-delay="200">
                    <div class="case-image">
                        <img src="../assets/images/case5.webp" alt="煤矿生产调度系统升级">
                    </div>
                    <div class="case-content">
                        <div class="case-category">生产调度 · 井工煤矿</div>
                        <h3>大型煤矿集团智能调度指挥中心建设</h3>
                        <p>为大型煤矿集团建设集中式智能调度指挥中心，整合人员定位、设备监控、产量统计等多系统数据，实现生产全流程可视化管理，效率提升38%，应急响应时间缩短65%。</p>
                        <a href="case-saas-sales.html" class="read-more">查看案例 <i class="fas fa-arrow-right"></i></a>
                    </div>
                </div>

                <!-- 案例6 -->
                <div class="case-card" data-industry="nonmetal" data-challenge="safety" data-aos="fade-up" data-aos-delay="300">
                    <div class="case-image">
                        <img src="../assets/images/case6.webp" alt="非金属矿山安全监测系统">
                    </div>
                    <div class="case-content">
                        <div class="case-category">安全监测 · 非金属矿山</div>
                        <h3>大型磷矿安全监测预警系统建设</h3>
                        <p>为某大型磷矿设计部署全面的安全监测预警系统，包括边坡稳定性监测、排土场监测、尾矿库监测等多子系统，实现风险源的自动识别与预警，安全管理效率提升54%。</p>
                        <a href="case-insurance-team.html" class="read-more">查看案例 <i class="fas fa-arrow-right"></i></a>
                    </div>
                </div>
            </div>

            <!-- 加载更多按钮 -->
            <div class="load-more-container" data-aos="fade-up" data-aos-delay="400">
                <button class="btn btn-outline">加载更多案例</button>
            </div>

            <!-- 客户评价部分 -->
            <section class="testimonials-section" style="padding: 80px 0 40px;">
                <h2 style="text-align: center; margin-bottom: 40px; color: var(--dark-blue); font-size: 2rem;" data-aos="fade-up">客户评价</h2>
                
                <div class="testimonial-swiper" data-aos="fade-up" data-aos-delay="200">
                <div class="swiper-wrapper">
                        <!-- 评论1 -->
                    <div class="swiper-slide">
                        <div class="testimonial">
                            <div class="testimonial-content">
                                    <p>"张工的智能化解决方案帮助我们矿区实现了安全生产的质的飞跃。系统上线后，安全事故率下降了45%，工人的工作环境也得到了显著改善。"</p>
                            </div>
                            <div class="testimonial-author">
                                    <img src="../assets/images/testimonials/client8.webp" alt="王总评价" class="optimize-image">
                                <div class="author-info">
                                        <h4>王总</h4>
                                        <p>陕西某大型煤矿集团 安全总监</p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 评论2 -->
                    <div class="swiper-slide">
                        <div class="testimonial">
                            <div class="testimonial-content">
                                    <p>"通过智能化改造，我们的生产效率提升了42%，每年可以节省成本近千万。张工团队的专业水平和服务态度都让我们非常满意。"</p>
                            </div>
                            <div class="testimonial-author">
                                    <img src="../assets/images/testimonials/client9.webp" alt="刘总评价" class="optimize-image">
                                <div class="author-info">
                                        <h4>刘总</h4>
                                        <p>山西某矿业集团 生产总监</p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 评论3 -->
                    <div class="swiper-slide">
                        <div class="testimonial">
                            <div class="testimonial-content">
                                    <p>"数字孪生系统的应用让我们对整个矿区的运营状况了如指掌。决策效率提升明显，现在我们可以更快速准确地应对各种生产情况。"</p>
                            </div>
                            <div class="testimonial-author">
                                    <img src="../assets/images/testimonials/client12.webp" alt="李总评价" class="optimize-image">
                                <div class="author-info">
                                        <h4>李总</h4>
                                        <p>内蒙古某矿业公司 技术总监</p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 评论4 -->
                    <div class="swiper-slide">
                        <div class="testimonial">
                            <div class="testimonial-content">
                                    <p>"自从采用了张工团队的智能矿山解决方案，我们的生产管理效率显著提升，设备故障率降低了35%，年度维护成本节省超过200万。"</p>
                            </div>
                            <div class="testimonial-author">
                                    <img src="../assets/images/testimonials/client11.webp" alt="张总评价" class="optimize-image">
                                <div class="author-info">
                                        <h4>张总</h4>
                                        <p>新疆某大型矿业集团 设备总监</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- 添加导航按钮 -->
                    <div class="swiper-button-next"></div>
                    <div class="swiper-button-prev"></div>
                    <!-- 添加分页器 -->
                    <div class="swiper-pagination"></div>
                </div>
            </section>

            <!-- 分页导航 -->
            <div style="margin-top: 4rem; text-align: center;" data-aos="fade-up" data-aos-delay="400">
                <ul style="display: inline-flex; list-style: none; gap: 5px;">
                    <li><a href="index.html" style="display: flex; align-items: center; justify-content: center; width: 40px; height: 40px; border-radius: 4px; background-color: var(--primary-color); color: var(--white); text-decoration: none;">1</a></li>
                    <li><a href="page-2.html" style="display: flex; align-items: center; justify-content: center; width: 40px; height: 40px; border-radius: 4px; background-color: var(--light-gray); color: var(--primary-color); text-decoration: none;">2</a></li>
                    <li><a href="page-2.html" style="display: flex; align-items: center; justify-content: center; width: 40px; height: 40px; border-radius: 4px; background-color: var(--light-gray); color: var(--primary-color); text-decoration: none;"><i class="fas fa-chevron-right"></i></a></li>
                </ul>
            </div>
        </div>
    </section>


    <!-- 行动召唤 -->
    <section class="cta-section" data-aos="zoom-in" data-aos-duration="800">
        <div class="container">
            <div class="cta-content">
                <h2>想了解我如何帮助您解决矿山信息化挑战？</h2>
                <p>无论您面临的是安全生产、智能开采还是数字化转型方面的挑战，我都可以提供专业解决方案。</p>
                <a href="../contact.html" class="btn btn-light">预约咨询</a>
            </div>
        </div>
    </section>

    <!-- 页脚 -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-logo">
                    <h3 style="color: var(--white);">张<span style="color: var(--secondary-color);">洁贞</span></h3>
                    <p>中矿天智信息科技(徐州)有限公司高级销售经理，专注矿山智能化解决方案。</p>
                    <div class="social-icons">
                        <a href="#" class="social-icon" title="抖音">
                            <img src="../assets/images/svg/douyin.svg" alt="抖音" style="width: 16px; height: 16px;">
                        </a>
                        <a href="#" class="social-icon" title="小红书">
                            <img src="../assets/images/svg/xiaohongshu.svg" alt="小红书" style="width: 16px; height: 16px;">
                        </a>
                        <a href="#" class="social-icon" title="微信">
                            <img src="../assets/images/svg/weixin.svg" alt="微信" style="width: 16px; height: 16px;">
                        </a>
                        <a href="#" class="social-icon" title="播客">
                            <img src="../assets/images/svg/boke.svg" alt="播客" style="width: 16px; height: 16px;">
                        </a>
                    </div>
                </div>
                
                <div class="footer-links">
                    <h4>网站导航</h4>
                    <ul>
                        <li><a href="../index.html">首页</a></li>
                        <li><a href="../about.html">关于我</a></li>
                        <li><a href="../blog/index.html">专业洞见</a></li>
                        <li><a href="index.html">成功案例</a></li>
                        <li><a href="../contact.html">联系方式</a></li>
                    </ul>
                </div>
                
                <div class="footer-links">
                    <h4>联系方式</h4>
                    <ul>
                        <li>
                            <img src="../assets/images/svg/email.svg" alt="邮箱" style="width: 16px; height: 16px; margin-right: 8px; vertical-align: middle;">
                            <a href="mailto:<EMAIL>"><EMAIL></a>
                        </li>
                        <li>
                            <img src="../assets/images/svg/dianhua.svg" alt="电话" style="width: 16px; height: 16px; margin-right: 8px; vertical-align: middle;">
                            <a href="tel:+8613938155869">+86 139 3815 5869</a>
                        </li>
                        <li>
                            <img src="../assets/images/svg/weixin.svg" alt="微信" style="width: 16px; height: 16px; margin-right: 8px; vertical-align: middle;">
                            <span style="color: var(--light-gray);">zhangjiezhen176</span>
                        </li>
                    </ul>
                </div>
            </div>
            
            <div class="footer-bottom">
                <p>© 2025 张洁贞 - 矿业信息化解决方案专家</p>
            </div>
        </div>
    </footer>

    <!-- JavaScript -->
    <script>
        // 安全初始化NProgress
        document.addEventListener('DOMContentLoaded', function() {
            // 确保页面显示
            document.body.classList.add('ready');
            
            // 确保document.body已经可用
            if (document.body) {
                NProgress.configure({ 
                    showSpinner: true,
                    easing: 'ease',
                    speed: 500
                });
                NProgress.start();
                
                window.addEventListener('load', function() {
                    NProgress.done();
                    // 强制设置body为可见
                    document.body.style.opacity = '1';
                });
            }
            
            // 确保AOS已加载后再初始化
            if (typeof AOS !== 'undefined') {
                AOS.init({
                    duration: 800,           // 动画持续时间
                    easing: 'ease-in-out',   // 动画缓动函数
                    once: true,              // 动画是否只播放一次
                    mirror: false,           // 滚动向上时是否重新播放动画
                    offset: 100              // 触发动画的位置偏移
                });
            }
            
            // 初始化Swiper轮播
            if (typeof Swiper === 'function') {
                try {
                    new Swiper('.testimonial-swiper', {
                        slidesPerView: 1,
                        spaceBetween: 30,
                        loop: true,
                        autoplay: {
                            delay: 5000,
                            disableOnInteraction: false,
                        },
                        pagination: {
                            el: '.swiper-pagination',
                            clickable: true,
                        },
                        navigation: {
                            nextEl: '.swiper-button-next',
                            prevEl: '.swiper-button-prev',
                        },
                        speed: 800
                    });
                } catch (error) {
                    console.error('Swiper初始化失败:', error);
                }
            }
            
            // 筛选功能逻辑
            const filterIndustry = document.getElementById('industry-filter');
            const filterChallenge = document.getElementById('challenge-filter');
            const caseCards = document.querySelectorAll('.case-card');

            if (filterIndustry && filterChallenge) {
                // 筛选变化时更新显示
                filterIndustry.addEventListener('change', updateFilters);
                filterChallenge.addEventListener('change', updateFilters);
                
                // 筛选功能
                function updateFilters() {
                    const industryValue = filterIndustry.value;
                    const challengeValue = filterChallenge.value;
                    
                    caseCards.forEach(card => {
                        const industryMatch = industryValue === 'all' || card.dataset.industry === industryValue;
                        const challengeMatch = challengeValue === 'all' || card.dataset.challenge === challengeValue;
                        
                        if (industryMatch && challengeMatch) {
                            card.style.display = 'block';
                        } else {
                            card.style.display = 'none';
                        }
                    });
                }
            }
        });
    </script>
    
    <!-- 延迟加载非关键JavaScript -->
    <script src="../assets/js/utils.js" defer></script>
    <script src="../assets/js/navigation.js" defer></script>
</body>
</html> 