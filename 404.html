<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>页面未找到 - 张洁贞的矿业信息化解决方案</title>
    <link rel="stylesheet" href="assets/css/main.css">
    <link rel="stylesheet" href="assets/css/responsive.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <!-- 网站图标 -->
    <link rel="icon" href="assets/images/favicon.ico">
    <!-- AOS动画库 -->
    <link rel="stylesheet" href="https://cdn.bootcdn.net/ajax/libs/aos/2.3.4/aos.css">
    <script src="https://cdn.bootcdn.net/ajax/libs/aos/2.3.4/aos.js" defer></script>
    <!-- NProgress加载进度条 -->
    <link rel="stylesheet" href="https://cdn.bootcdn.net/ajax/libs/nprogress/0.2.0/nprogress.min.css">
    <script src="https://cdn.bootcdn.net/ajax/libs/nprogress/0.2.0/nprogress.min.js"></script>
    <style>
        /* 自定义NProgress样式 */
        #nprogress .bar {
            background: var(--primary-color);
            height: 3px;
        }
        #nprogress .peg {
            box-shadow: 0 0 10px var(--primary-color), 0 0 5px var(--primary-color);
        }
        #nprogress .spinner-icon {
            border-top-color: var(--primary-color);
            border-left-color: var(--primary-color);
        }
        
        /* 404页面特定样式 */
        .error-container {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            text-align: center;
            min-height: 70vh;
            padding: 2rem 1rem;
        }
        
        .error-code {
            font-size: 12rem;
            font-weight: 700;
            color: var(--primary-color);
            opacity: 0.3;
            margin: 0;
            line-height: 1;
        }
        
        .error-title {
            font-size: 2.5rem;
            margin-top: -2rem;
            margin-bottom: 1.5rem;
            color: var(--dark-blue);
        }
        
        .error-text {
            font-size: 1.2rem;
            max-width: 600px;
            margin-bottom: 2.5rem;
            color: var(--gray);
        }
        
        .error-actions {
            display: flex;
            gap: 1rem;
            margin-bottom: 3rem;
        }
        
        .error-image {
            max-width: 100%;
            width: 400px;
            height: auto;
            margin-bottom: 2rem;
        }
        
        .btn-home {
            background-color: var(--primary-color);
            color: white;
            padding: 0.8rem 2rem;
            border-radius: 4px;
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .btn-home:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
        }
        
        .btn-contact {
            background-color: transparent;
            color: var(--primary-color);
            border: 2px solid var(--primary-color);
            padding: 0.8rem 2rem;
            border-radius: 4px;
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .btn-contact:hover {
            background-color: var(--primary-color);
            color: white;
            transform: translateY(-3px);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
        }
        
        /* 确保导航栏样式正确 */
        .navbar {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        @media (max-width: 992px) {
            .mobile-nav-toggle {
                display: block;
                order: 3;
            }
            .navbar {
                justify-content: space-between;
            }
            .nav-menu {
                display: none;
            }
            .logo {
                flex-grow: 1;
            }
            .error-code {
                font-size: 8rem;
            }
            .error-title {
                font-size: 2rem;
                margin-top: -1rem;
            }
            .error-actions {
                flex-direction: column;
            }
        }
        
        @media (min-width: 993px) {
            .mobile-nav-toggle {
                display: none;
            }
        }
    </style>
</head>
<body>
    <!-- 顶部导航栏 -->
    <header class="header">
        <div class="container">
            <nav class="navbar">
                <a href="index.html" class="logo">张<span>洁贞</span><span class="logo-subtitle">矿业信息化专家</span></a>
                <ul class="nav-menu">
                    <li class="nav-item"><a href="index.html" class="nav-link">首页</a></li>
                    <li class="nav-item"><a href="about.html" class="nav-link">关于我</a></li>
                    <li class="nav-item"><a href="blog/index.html" class="nav-link">洞见</a></li>
                    <li class="nav-item"><a href="case-studies/index.html" class="nav-link">案例</a></li>
                    <li class="nav-item"><a href="contact.html" class="nav-link">联系</a></li>
                    <li class="nav-item"><a href="contact.html" class="nav-contact-btn"><i class="fas fa-headset"></i>咨询我</a></li>
                </ul>
                <button class="mobile-nav-toggle">
                    <i class="fas fa-bars"></i>
                </button>
            </nav>
        </div>
    </header>

    <!-- 移动导航菜单 -->
    <div class="mobile-nav-wrapper">
        <div class="mobile-nav">
            <button class="close-mobile-nav">
                <i class="fas fa-times"></i>
            </button>
            <ul>
                <li><a href="index.html">首页</a></li>
                <li><a href="about.html">关于我</a></li>
                <li><a href="blog/index.html">洞见</a></li>
                <li><a href="case-studies/index.html">案例</a></li>
                <li><a href="contact.html">联系</a></li>
            </ul>
        </div>
        <div class="mobile-nav-backdrop"></div>
    </div>
    
    <!-- 404错误内容 -->
    <section class="error-container" data-aos="fade-up" data-aos-duration="800">
        <h1 class="error-code">404</h1>
        <h2 class="error-title">页面未找到</h2>
        <p class="error-text">很抱歉，您尝试访问的页面不存在或已被移除。请尝试访问其他页面或联系我们获取帮助。</p>
        
        <img src="assets/images/404.webp" alt="404错误图示" class="error-image" data-aos="zoom-in" data-aos-delay="200">
        
        <div class="error-actions">
            <a href="index.html" class="btn-home">
                <i class="fas fa-home"></i> 返回首页
            </a>
            <a href="contact.html" class="btn-contact">
                <i class="fas fa-envelope"></i> 联系我们
            </a>
        </div>
    </section>
    
    <!-- 页脚 -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-logo">
                    <h3 style="color: var(--white);">张<span style="color: var(--secondary-color);">洁贞</span></h3>
                    <p>中矿天智信息科技(徐州)有限公司高级销售经理，专注矿山智能化解决方案。</p>
                    <div class="social-icons">
                        <a href="#" class="social-icon" title="抖音">
                            <img src="assets/images/svg/douyin.svg" alt="抖音" style="width: 16px; height: 16px;">
                        </a>
                        <a href="#" class="social-icon" title="小红书">
                            <img src="assets/images/svg/xiaohongshu.svg" alt="小红书" style="width: 16px; height: 16px;">
                        </a>
                        <a href="#" class="social-icon" title="微信">
                            <img src="assets/images/svg/weixin.svg" alt="微信" style="width: 16px; height: 16px;">
                        </a>
                        <a href="#" class="social-icon" title="播客">
                            <img src="assets/images/svg/boke.svg" alt="播客" style="width: 16px; height: 16px;">
                        </a>
                    </div>
                </div>
                
                <div class="footer-links">
                    <h4>网站导航</h4>
                    <ul>
                        <li><a href="index.html">首页</a></li>
                        <li><a href="about.html">关于我</a></li>
                        <li><a href="blog/index.html">专业洞见</a></li>
                        <li><a href="case-studies/index.html">成功案例</a></li>
                        <li><a href="contact.html">联系方式</a></li>
                    </ul>
                </div>
                
                <div class="footer-links">
                    <h4>联系方式</h4>
                    <ul>
                        <li>
                            <img src="assets/images/svg/email.svg" alt="邮箱" style="width: 16px; height: 16px; margin-right: 8px; vertical-align: middle;">
                            <a href="mailto:<EMAIL>"><EMAIL></a>
                        </li>
                        <li>
                            <img src="assets/images/svg/dianhua.svg" alt="电话" style="width: 16px; height: 16px; margin-right: 8px; vertical-align: middle;">
                            <a href="tel:+8613938155869">+86 139 3815 5869</a>
                        </li>
                        <li>
                            <img src="assets/images/svg/weixin.svg" alt="微信" style="width: 16px; height: 16px; margin-right: 8px; vertical-align: middle;">
                            <span style="color: var(--light-gray);">zhangjiezhen176</span>
                        </li>
                    </ul>
                </div>
            </div>
            
            <div class="footer-bottom">
                <p>© 2025 张洁贞 - 矿业信息化解决方案专家</p>
            </div>
        </div>
    </footer>

    <!-- JavaScript -->
    <script>
        // 页面加载进度条
        NProgress.configure({ 
            showSpinner: true,
            easing: 'ease',
            speed: 500
        });
        NProgress.start();
        
        document.addEventListener('DOMContentLoaded', function() {
            // 页面加载完成后结束进度条
            NProgress.done();
            
            // 初始化AOS动画库
            AOS.init({
                duration: 800,
                easing: 'ease-in-out',
                once: true,
                mirror: false,
                offset: 100
            });
            
            // 移动导航菜单
            const mobileNavToggle = document.querySelector('.mobile-nav-toggle');
            const closeNavBtn = document.querySelector('.close-mobile-nav');
            const mobileNavWrapper = document.querySelector('.mobile-nav-wrapper');
            const mobileNavBackdrop = document.querySelector('.mobile-nav-backdrop');
            
            if(mobileNavToggle && closeNavBtn && mobileNavWrapper) {
                mobileNavToggle.addEventListener('click', () => {
                    mobileNavWrapper.classList.add('active');
                    document.body.style.overflow = 'hidden';
                });
                
                function closeNav() {
                    mobileNavWrapper.classList.remove('active');
                    document.body.style.overflow = '';
                }
                
                closeNavBtn.addEventListener('click', closeNav);
                mobileNavBackdrop.addEventListener('click', closeNav);
            }
        });
    </script>
    
    <!-- 延迟加载非关键JavaScript -->
    <script src="assets/js/utils.js" defer></script>
    <script src="assets/js/navigation.js" defer></script>
</body>
</html>