<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>专业洞见 - 张洁贞 | 矿业信息化解决方案专家</title>
    <meta name="description" content="来自张洁贞的矿业信息化专业洞见，涵盖安全监测系统、智能开采平台、矿井通风优化等多个领域的实战经验分享。">
    
    <!-- CSS 文件 -->
    <link rel="stylesheet" href="../assets/css/main.css">
    <link rel="stylesheet" href="../assets/css/responsive.css">
    
    <!-- 网站图标 -->
    <link rel="icon" href="../assets/images/favicon.ico">
    
    <!-- Font Awesome 图标库 -->
    <link rel="stylesheet" href="https://cdn.bootcdn.net/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    
    <!-- AOS动画库 -->
    <link rel="stylesheet" href="https://cdn.bootcdn.net/ajax/libs/aos/2.3.4/aos.css">
    <script src="https://cdn.bootcdn.net/ajax/libs/aos/2.3.4/aos.js" defer></script>
    
    <!-- NProgress加载进度条 -->
    <link rel="stylesheet" href="https://cdn.bootcdn.net/ajax/libs/nprogress/0.2.0/nprogress.min.css">
    <script src="https://cdn.bootcdn.net/ajax/libs/nprogress/0.2.0/nprogress.min.js"></script>
</head>
<body>
    <!-- 顶部导航栏 -->
    <header class="header">
        <div class="container">
            <nav class="navbar">
                <a href="../index.html" class="logo">张<span>洁贞</span><span class="logo-subtitle">矿业信息化专家</span></a>
                <ul class="nav-menu">
                    <li class="nav-item"><a href="../index.html" class="nav-link">首页</a></li>
                    <li class="nav-item"><a href="../about.html" class="nav-link">关于我</a></li>
                    <li class="nav-item"><a href="../blog/index.html" class="nav-link active">洞见</a></li>
                    <li class="nav-item"><a href="../case-studies/index.html" class="nav-link">案例</a></li>
                    <li class="nav-item"><a href="../contact.html" class="nav-link">联系</a></li>
                    <li class="nav-item"><a href="../contact.html" class="nav-contact-btn"><i class="fas fa-headset"></i>咨询我</a></li>
                </ul>
                <button class="mobile-nav-toggle">
                    <i class="fas fa-bars"></i>
                </button>
            </nav>
        </div>
    </header>
    
    <!-- 页面标题区 -->
    <section style="position: relative; background: url('../assets/images/background-texture2.webp') center/cover; padding: 120px 0 80px;">
        <div style="position: absolute; top: 0; left: 0; right: 0; bottom: 0; background: linear-gradient(135deg, rgba(26, 54, 93, 0.45), rgba(21, 32, 50, 0.65));"></div>
        <div class="container" style="position: relative; z-index: 2;">
            <h1 style="color: var(--white); margin-bottom: 1rem; font-size: 2.8rem; text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);" data-aos="fade-up" data-aos-delay="100">专业洞见</h1>
            <p style="font-size: 1.2rem; max-width: 700px; color: rgba(255, 255, 255, 0.95); text-shadow: 0 1px 3px rgba(0, 0, 0, 0.2); line-height: 1.6;" data-aos="fade-up" data-aos-delay="200">分享矿业信息化领域的前沿观点和实践经验</p>
        </div>
    </section>
    
    <!-- 博客分类导航 -->
    <section style="background-color: var(--white); border-bottom: 1px solid #eee; padding: 20px 0;" data-aos="fade-up" data-aos-delay="300">
        <div class="container">
            <ul style="display: flex; flex-wrap: wrap; gap: 15px; list-style: none;">
                <li><a href="#" style="padding: 8px 15px; border-radius: 20px; background-color: var(--primary-color); color: var(--white); text-decoration: none; font-size: 0.9rem;">全部</a></li>
                <li><a href="#" style="padding: 8px 15px; border-radius: 20px; background-color: var(--light-gray); color: var(--primary-color); text-decoration: none; font-size: 0.9rem;">安全监测</a></li>
                <li><a href="#" style="padding: 8px 15px; border-radius: 20px; background-color: var(--light-gray); color: var(--primary-color); text-decoration: none; font-size: 0.9rem;">智能开采</a></li>
                <li><a href="#" style="padding: 8px 15px; border-radius: 20px; background-color: var(--light-gray); color: var(--primary-color); text-decoration: none; font-size: 0.9rem;">通风优化</a></li>
                <li><a href="#" style="padding: 8px 15px; border-radius: 20px; background-color: var(--light-gray); color: var(--primary-color); text-decoration: none; font-size: 0.9rem;">数字孪生</a></li>
                <li><a href="#" style="padding: 8px 15px; border-radius: 20px; background-color: var(--light-gray); color: var(--primary-color); text-decoration: none; font-size: 0.9rem;">智慧矿山</a></li>
                <li><a href="#" style="padding: 8px 15px; border-radius: 20px; background-color: var(--light-gray); color: var(--primary-color); text-decoration: none; font-size: 0.9rem;">技术前沿</a></li>
            </ul>
        </div>
    </section>
    
    <!-- 搜索栏 -->
    <section style="padding: 30px 0; background-color: var(--white);" data-aos="fade-up" data-aos-delay="400">
        <div class="container">
            <div style="max-width: 600px; margin: 0 auto;">
                <form style="display: flex; gap: 10px;">
                    <input type="text" placeholder="搜索关键词..." style="flex: 1; padding: 12px 15px; border: 1px solid #ddd; border-radius: 4px; font-family: var(--body-font);">
                    <button type="submit" style="background-color: var(--primary-color); color: var(--white); border: none; border-radius: 4px; padding: 0 20px; cursor: pointer;">
                        <i class="fas fa-search"></i>
                    </button>
                </form>
            </div>
        </div>
    </section>
    
    <!-- 博客文章列表 -->
    <section class="section">
        <div class="container">
            <!-- 精选文章 -->
            <div style="margin-bottom: 4rem;">
                <div style="margin-bottom: 2rem;" data-aos="fade-up" data-aos-delay="500">
                    <h2>精选文章</h2>
                </div>
                
                <div style="display: flex; flex-wrap: wrap; gap: 30px;">
                    <!-- 精选文章1 -->
                    <div style="flex: 1; min-width: 100%; display: flex; flex-wrap: wrap; background: var(--white); border-radius: 8px; overflow: hidden; box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);" data-aos="zoom-in" data-aos-delay="600">
                        <div style="flex: 1; min-width: 300px;">
                            <img src="../assets/images/blog/featured-article.webp" alt="矿山安全监测预警系统的构建与应用" style="width: 100%; height: 100%; object-fit: cover;">
                        </div>
                        <div style="flex: 1; min-width: 300px; padding: 30px;">
                            <span style="display: inline-block; padding: 5px 10px; background-color: var(--light-gray); color: var(--primary-color); font-size: 0.8rem; border-radius: 4px; margin-bottom: 15px;">安全监测</span>
                            <h3 style="font-size: 1.8rem; margin-bottom: 15px;">矿山安全监测预警系统的构建与应用</h3>
                            <p style="margin-bottom: 20px; color: var(--medium-gray);">2023年12月20日</p>
                            <p style="margin-bottom: 20px;">现代煤矿企业安全生产的首要保障，源于科学完善的监测预警体系。本文深入分析了基于物联网技术的安全监测系统如何有效预防事故发生，并分享了实际应用案例...</p>
                            <a href="smart-mining-safety.html" class="btn">阅读全文</a>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 最新文章 -->
            <div>
                <div style="margin-bottom: 2rem;" data-aos="fade-up" data-aos-delay="700">
                    <h2>最新发布</h2>
                </div>
                
                <div class="blog-grid">
                    <!-- 文章1 -->
                    <div class="blog-card" data-aos="fade-up" data-aos-delay="800">
                        <div class="blog-card-image">
                            <img src="../assets/images/blog/article-thumbnail-1.webp" alt="矿山安全监测预警系统的构建与应用">
                        </div>
                        <div class="blog-card-content">
                            <span style="display: inline-block; padding: 5px 10px; background-color: var(--light-gray); color: var(--primary-color); font-size: 0.8rem; border-radius: 4px; margin-bottom: 10px;">安全监测</span>
                            <span class="blog-card-date">2023年12月15日</span>
                            <h3 class="blog-card-title">矿山安全监测预警系统的构建与应用</h3>
                            <p class="blog-card-excerpt">现代煤矿企业安全生产的首要保障，源于科学完善的监测预警体系。本文分析了基于物联网技术的安全监测系统如何有效预防事故发生...</p>
                            <a href="safety-monitoring.html" class="btn btn-secondary">阅读全文</a>
                        </div>
                    </div>
                    
                    <!-- 文章2 -->
                    <div class="blog-card" data-aos="fade-up" data-aos-delay="900">
                        <div class="blog-card-image">
                            <img src="../assets/images/blog/article-thumbnail-2.webp" alt="煤矿智能开采关键技术与实践案例">
                        </div>
                        <div class="blog-card-content">
                            <span style="display: inline-block; padding: 5px 10px; background-color: var(--light-gray); color: var(--primary-color); font-size: 0.8rem; border-radius: 4px; margin-bottom: 10px;">智能开采</span>
                            <span class="blog-card-date">2023年11月28日</span>
                            <h3 class="blog-card-title">煤矿智能开采关键技术与实践案例</h3>
                            <p class="blog-card-excerpt">智能开采是煤矿企业降本增效的核心手段。本文详细介绍了工作面智能化开采系统的关键技术及其在陕西某煤矿的成功应用案例...</p>
                            <a href="intelligent-mining-tech.html" class="btn btn-secondary">阅读全文</a>
                        </div>
                    </div>
                    
                    <!-- 文章3 -->
                    <div class="blog-card" data-aos="fade-up" data-aos-delay="1000">
                        <div class="blog-card-image">
                            <img src="../assets/images/blog/article-thumbnail-3.webp" alt="数字孪生技术在现代煤矿中的应用前景">
                        </div>
                        <div class="blog-card-content">
                            <span style="display: inline-block; padding: 5px 10px; background-color: var(--light-gray); color: var(--primary-color); font-size: 0.8rem; border-radius: 4px; margin-bottom: 10px;">数字孪生</span>
                            <span class="blog-card-date">2023年10月10日</span>
                            <h3 class="blog-card-title">数字孪生技术在现代煤矿中的应用前景</h3>
                            <p class="blog-card-excerpt">数字孪生技术为煤矿提供了虚实结合的智能管理新模式，实现了从被动应对到主动预测的转变。本文探讨了该技术在煤矿领域的创新应用...</p>
                            <a href="digital-twin.html" class="btn btn-secondary">阅读全文</a>
                        </div>
                    </div>
                    
                    <!-- 文章4 -->
                    <div class="blog-card" data-aos="fade-up" data-aos-delay="800">
                        <div class="blog-card-image">
                            <img src="../assets/images/blog/article-thumbnail-4.webp" alt="智慧矿山建设的总体框架与实施路径">
                        </div>
                        <div class="blog-card-content">
                            <span style="display: inline-block; padding: 5px 10px; background-color: var(--light-gray); color: var(--primary-color); font-size: 0.8rem; border-radius: 4px; margin-bottom: 10px;">智慧矿山</span>
                            <span class="blog-card-date">2023年9月22日</span>
                            <h3 class="blog-card-title">智慧矿山建设的总体框架与实施路径</h3>
                            <p class="blog-card-excerpt">智慧矿山是煤炭行业数字化转型的重要方向。本文提出了智慧矿山建设的总体框架，并分析了从规划到落地的分步实施路径...</p>
                            <a href="smart-mine-framework.html" class="btn btn-secondary">阅读全文</a>
                        </div>
                    </div>
                    
                    <!-- 文章5 -->
                    <div class="blog-card" data-aos="fade-up" data-aos-delay="900">
                        <div class="blog-card-image">
                            <img src="../assets/images/blog/article-thumbnail-5.webp" alt="矿井通风智能优化系统设计与应用效果分析">
                        </div>
                        <div class="blog-card-content">
                            <span style="display: inline-block; padding: 5px 10px; background-color: var(--light-gray); color: var(--primary-color); font-size: 0.8rem; border-radius: 4px; margin-bottom: 10px;">通风优化</span>
                            <span class="blog-card-date">2023年8月15日</span>
                            <h3 class="blog-card-title">矿井通风智能优化系统设计与应用效果分析</h3>
                            <p class="blog-card-excerpt">矿井通风是煤矿安全生产的重要保障。本文介绍了基于CFD模拟和智能算法的矿井通风优化系统，并分析了在实际矿井中的应用效果...</p>
                            <a href="ventilation-optimization.html" class="btn btn-secondary">阅读全文</a>
                        </div>
                    </div>
                    
                    <!-- 文章6 -->
                    <div class="blog-card" data-aos="fade-up" data-aos-delay="1000">
                        <div class="blog-card-image">
                            <img src="../assets/images/blog/article-thumbnail-6.webp" alt="5G+AI技术在煤矿智能巡检中的创新应用">
                        </div>
                        <div class="blog-card-content">
                            <span style="display: inline-block; padding: 5px 10px; background-color: var(--light-gray); color: var(--primary-color); font-size: 0.8rem; border-radius: 4px; margin-bottom: 10px;">技术前沿</span>
                            <span class="blog-card-date">2023年7月20日</span>
                            <h3 class="blog-card-title">5G+AI技术在煤矿智能巡检中的创新应用</h3>
                            <p class="blog-card-excerpt">5G与人工智能技术的结合为煤矿智能巡检带来了革命性变化。本文分享了利用5G网络和AI识别技术打造无人智能巡检系统的成功案例...</p>
                            <a href="5g-ai-inspection.html" class="btn btn-secondary">阅读全文</a>
                        </div>
                    </div>
                </div>
                
                <!-- 分页 -->
                <div style="margin-top: 4rem; text-align: center;" data-aos="fade-up" data-aos-delay="1100">
                    <ul style="display: inline-flex; list-style: none; gap: 5px;">
                        <li><a href="index.html" style="display: flex; align-items: center; justify-content: center; width: 40px; height: 40px; border-radius: 4px; background-color: var(--primary-color); color: var(--white); text-decoration: none;">1</a></li>
                        <li><a href="page-2.html" style="display: flex; align-items: center; justify-content: center; width: 40px; height: 40px; border-radius: 4px; background-color: var(--light-gray); color: var(--primary-color); text-decoration: none;">2</a></li>
                        <li><a href="page-3.html" style="display: flex; align-items: center; justify-content: center; width: 40px; height: 40px; border-radius: 4px; background-color: var(--light-gray); color: var(--primary-color); text-decoration: none;">3</a></li>
                        <li><a href="page-2.html" style="display: flex; align-items: center; justify-content: center; width: 40px; height: 40px; border-radius: 4px; background-color: var(--light-gray); color: var(--primary-color); text-decoration: none;"><i class="fas fa-chevron-right"></i></a></li>
                    </ul>
                </div>
            </div>
        </div>
    </section>
    
    <!-- 页脚 -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-logo">
                    <h3 style="color: var(--white);">张<span style="color: var(--secondary-color);">洁贞</span></h3>
                    <p>中矿天智信息科技(徐州)有限公司高级销售经理，专注矿山智能化解决方案。</p>
                    <div class="social-icons">
                        <a href="#" class="social-icon" title="抖音">
                            <img src="../assets/images/svg/douyin.svg" alt="抖音" style="width: 16px; height: 16px;">
                        </a>
                        <a href="#" class="social-icon" title="小红书">
                            <img src="../assets/images/svg/xiaohongshu.svg" alt="小红书" style="width: 16px; height: 16px;">
                        </a>
                        <a href="#" class="social-icon" title="微信">
                            <img src="../assets/images/svg/weixin.svg" alt="微信" style="width: 16px; height: 16px;">
                        </a>
                        <a href="#" class="social-icon" title="播客">
                            <img src="../assets/images/svg/boke.svg" alt="播客" style="width: 16px; height: 16px;">
                        </a>
                    </div>
                </div>
                
                <div class="footer-links">
                    <h4>网站导航</h4>
                    <ul>
                        <li><a href="../index.html">首页</a></li>
                        <li><a href="../about.html">关于我</a></li>
                        <li><a href="../blog/index.html">专业洞见</a></li>
                        <li><a href="../case-studies/index.html">成功案例</a></li>
                        <li><a href="../contact.html">联系方式</a></li>
                    </ul>
                </div>
                
                <div class="footer-links">
                    <h4>联系方式</h4>
                    <ul>
                        <li>
                            <img src="../assets/images/svg/email.svg" alt="邮箱" style="width: 16px; height: 16px; margin-right: 8px; vertical-align: middle;">
                            <a href="mailto:<EMAIL>"><EMAIL></a>
                        </li>
                        <li>
                            <img src="../assets/images/svg/dianhua.svg" alt="电话" style="width: 16px; height: 16px; margin-right: 8px; vertical-align: middle;">
                            <a href="tel:+8613938155869">+86 139 3815 5869</a>
                        </li>
                        <li>
                            <img src="../assets/images/svg/weixin.svg" alt="微信" style="width: 16px; height: 16px; margin-right: 8px; vertical-align: middle;">
                            <span style="color: var(--light-gray);">zhangjiezhen176</span>
                        </li>
                    </ul>
                </div>
            </div>
            
            <div class="footer-bottom">
                <p>&copy; 2025 张洁贞 - 矿业信息化解决方案专家</p>
            </div>
        </div>
    </footer>
    
    <!-- 移动导航菜单 -->
    <div class="mobile-nav-wrapper">
        <div class="mobile-nav">
            <button class="close-mobile-nav">
                <i class="fas fa-times"></i>
            </button>
            <ul>
                <li><a href="../index.html">首页</a></li>
                <li><a href="../about.html">关于我</a></li>
                <li><a href="../blog/index.html" class="active">洞见</a></li>
                <li><a href="../case-studies/index.html">案例</a></li>
                <li><a href="../contact.html">联系</a></li>
            </ul>
        </div>
        <div class="mobile-nav-backdrop"></div>
    </div>
    
    <!-- JavaScript -->
    <script src="../assets/js/main.js"></script>
    
    <!-- AOS初始化脚本 -->
    <script>
        // 页面加载进度条
        NProgress.configure({ 
            showSpinner: true,
            easing: 'ease',
            speed: 500
        });
        NProgress.start();
        
        document.addEventListener('DOMContentLoaded', function() {
            // 页面加载完成后结束进度条
            NProgress.done();
            
            // 初始化AOS动画库
            AOS.init({
                duration: 800,           // 动画持续时间
                easing: 'ease-in-out',   // 动画缓动函数
                once: true,              // 动画是否只播放一次
                mirror: false,           // 滚动向上时是否重新播放动画
                offset: 100              // 触发动画的位置偏移
            });
            
            // 移动导航菜单功能
            const mobileNavToggle = document.querySelector('.mobile-nav-toggle');
            const mobileNav = document.querySelector('.mobile-nav');
            const closeBtn = document.querySelector('.close-mobile-nav');
            const backdrop = document.querySelector('.mobile-nav-backdrop');
            
            // 打开导航菜单
            if (mobileNavToggle) {
                mobileNavToggle.addEventListener('click', function() {
                    document.body.classList.add('mobile-nav-active');
                    mobileNav.classList.add('active');
                    document.body.style.overflow = 'hidden'; // 防止背景滚动
                });
            }
            
            // 关闭导航菜单的几种方式
            if (closeBtn) {
                closeBtn.addEventListener('click', closeNav);
            }
            
            if (backdrop) {
                backdrop.addEventListener('click', closeNav);
            }
            
            // 点击导航链接后关闭菜单
            const mobileNavLinks = document.querySelectorAll('.mobile-nav a');
            mobileNavLinks.forEach(link => {
                link.addEventListener('click', closeNav);
            });
            
            function closeNav() {
                document.body.classList.remove('mobile-nav-active');
                mobileNav.classList.remove('active');
                document.body.style.overflow = 'auto'; // 允许背景滚动
            }
            
            // 导航栏滚动效果
            const header = document.querySelector('.header');
            window.addEventListener('scroll', function() {
                if (window.scrollY > 100) {
                    header.classList.add('scrolled');
                } else {
                    header.classList.remove('scrolled');
                }
            });
            
            // 订阅表单处理
            const subscribeForm = document.querySelector('form');
            if (subscribeForm) {
                subscribeForm.addEventListener('submit', function(e) {
                    e.preventDefault();
                    const emailInput = this.querySelector('input[type="email"]');
                    if (!emailInput.value.trim()) {
                        alert('请输入有效的邮箱地址');
                        return;
                    }
                    
                    // 模拟订阅成功
                    alert('订阅成功！感谢您的关注。');
                    emailInput.value = '';
                });
            }
        });
    </script>
</body>
</html>