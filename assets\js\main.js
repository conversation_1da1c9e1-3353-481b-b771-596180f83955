/**
 * 导航栏功能管理
 * 包含：滚动效果、导航激活状态、移动导航
 */

document.addEventListener('DOMContentLoaded', () => {
    // 配置参数
    const SCROLL_THRESHOLD = 100;
    const header = document.querySelector('.header');
    const mobileNavToggle = document.querySelector('.mobile-nav-toggle');
    const mobileNavWrapper = document.querySelector('.mobile-nav-wrapper');
    const mobileNavClose = document.querySelector('.close-mobile-nav');
    const mobileNavBackdrop = document.querySelector('.mobile-nav-backdrop');
    
    /**
     * 处理页面滚动效果
     * 功能：根据滚动位置调整导航栏样式
     */
    function handleScroll() {
        if (window.scrollY > SCROLL_THRESHOLD) {
            header.classList.add('scrolled');
        } else {
            header.classList.remove('scrolled');
        }
    }

    /**
     * 设置导航项激活状态
     * 功能：根据当前URL路径高亮对应导航链接
     */
    function setActiveNavItems() {
        const currentPath = window.location.pathname;
        const navLinks = document.querySelectorAll('.nav-link, .mobile-nav a');
        
        navLinks.forEach(link => {
            const href = link.getAttribute('href');
            const fullPath = new URL(href, window.location.origin).pathname;
            
            // 移除所有active类
            link.classList.remove('active');
            
            // 设置active类的条件
            const isActive = 
                currentPath === fullPath || // 完全匹配
                (currentPath.endsWith('/') && fullPath === currentPath + 'index.html') || // 目录匹配
                (!currentPath.endsWith('/') && currentPath.replace(/\.html$/, '') === fullPath.replace(/\.html$/, '')); // 忽略.html后缀匹配
            
            if (isActive) {
                link.classList.add('active');
            }
        });
    }

    /**
     * 关闭移动导航
     * 功能：关闭移动导航菜单并恢复页面滚动
     */
    function closeNav() {
        mobileNavWrapper.classList.remove('active');
        document.body.style.overflow = 'auto';
    }

    // 事件监听器
    window.addEventListener('scroll', handleScroll);
    
    mobileNavToggle?.addEventListener('click', () => {
        mobileNavWrapper.classList.add('active');
        document.body.style.overflow = 'hidden';
    });
    
    mobileNavClose?.addEventListener('click', closeNav);
    mobileNavBackdrop?.addEventListener('click', closeNav);
    
    // 移动导航链接点击后自动关闭
    document.querySelector('.mobile-nav')?.addEventListener('click', (e) => {
        if (e.target.tagName === 'A') {
            closeNav();
        }
    });

    // 初始化
    handleScroll();
    setActiveNavItems();
}); 