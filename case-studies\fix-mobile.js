// 修复移动导航和轮播问题

// 在页面加载完成后执行
document.addEventListener('DOMContentLoaded', function() {
    // 确保移动导航在小屏幕上可见
    var mobileToggle = document.querySelector('.mobile-nav-toggle');
    if (mobileToggle) {
        mobileToggle.style.display = 'block';
        mobileToggle.style.visibility = 'visible';
        mobileToggle.style.opacity = '1';
        mobileToggle.style.zIndex = '9999';
    }
    
    // 初始化客户评价轮播，确保正常工作
    var testimonialSwiper = new Swiper('.testimonial-swiper', {
        slidesPerView: 1,
        spaceBetween: 30,
        loop: true,
        autoplay: {
            delay: 5000,
            disableOnInteraction: false,
        },
        pagination: {
            el: '.swiper-pagination',
            clickable: true,
        },
        navigation: {
            nextEl: '.swiper-button-next',
            prevEl: '.swiper-button-prev',
        },
        effect: 'slide',  // 使用滑动效果，更可靠
        speed: 600,
        observer: true,  // 当DOM元素变化时自动初始化Swiper
        observeParents: true,  // 当Swiper的父元素变化时，自动更新Swiper
        breakpoints: {
            // 移动端配置
            0: {
                slidesPerView: 1,
                spaceBetween: 20,
            },
            // 平板配置
            768: {
                slidesPerView: 1,
                spaceBetween: 30,
            }
        }
    });
    
    // 确保Swiper在各种情况下都能更新
    window.addEventListener('resize', function() {
        if (testimonialSwiper) {
            testimonialSwiper.update();
        }
    });
    
    // 延迟更新Swiper以确保DOM完全加载
    setTimeout(function() {
        if (testimonialSwiper) {
            testimonialSwiper.update();
        }
    }, 500);
});