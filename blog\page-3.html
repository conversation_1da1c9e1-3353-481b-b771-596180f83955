<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>专业洞见 - 第3页 - 张洁贞 | 矿业信息化解决方案专家</title>
    <meta name="description" content="来自张洁贞的矿业信息化专业洞见，涵盖安全监测系统、智能开采平台、矿井通风优化等多个领域的实战经验分享。">
    
    <!-- CSS 文件 -->
    <link rel="stylesheet" href="../assets/css/main.css">
    <link rel="stylesheet" href="../assets/css/responsive.css">
    
    <!-- 网站图标 -->
    <link rel="icon" href="../assets/images/favicon.ico">
    
    <!-- Font Awesome 图标库 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    
    <!-- AOS动画库 -->
    <link rel="stylesheet" href="https://cdn.bootcdn.net/ajax/libs/aos/2.3.4/aos.css">
    <script src="https://cdn.bootcdn.net/ajax/libs/aos/2.3.4/aos.js" defer></script>
    
    <!-- NProgress加载进度条 -->
    <link rel="stylesheet" href="https://cdn.bootcdn.net/ajax/libs/nprogress/0.2.0/nprogress.min.css">
    <script src="https://cdn.bootcdn.net/ajax/libs/nprogress/0.2.0/nprogress.min.js"></script>
</head>
<body>
    <!-- 顶部导航栏 -->
    <header class="header">
        <div class="container">
            <nav class="navbar">
                <a href="../index.html" class="logo">张<span>洁贞</span><span class="logo-subtitle">矿业信息化专家</span></a>
                <ul class="nav-menu">
                    <li class="nav-item"><a href="../index.html" class="nav-link">首页</a></li>
                    <li class="nav-item"><a href="../about.html" class="nav-link">关于我</a></li>
                    <li class="nav-item"><a href="../blog/index.html" class="nav-link active">洞见</a></li>
                    <li class="nav-item"><a href="../case-studies/index.html" class="nav-link">案例</a></li>
                    <li class="nav-item"><a href="../contact.html" class="nav-link">联系</a></li>
                    <li class="nav-item"><a href="../contact.html" class="nav-contact-btn"><i class="fas fa-headset"></i>咨询我</a></li>
                </ul>
                <button class="mobile-nav-toggle">
                    <i class="fas fa-bars"></i>
                </button>
            </nav>
        </div>
    </header>
    
    <!-- 页面标题区 -->
    <section style="position: relative; background: url('../assets/images/background-texture2.webp') center/cover; padding: 120px 0 80px;">
        <div style="position: absolute; top: 0; left: 0; right: 0; bottom: 0; background: linear-gradient(135deg, rgba(26, 54, 93, 0.45), rgba(21, 32, 50, 0.65));"></div>
        <div class="container" style="position: relative; z-index: 2;">
            <h1 style="color: var(--white); margin-bottom: 1rem; font-size: 2.8rem; text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);" data-aos="fade-up" data-aos-delay="100">专业洞见</h1>
            <p style="font-size: 1.2rem; max-width: 700px; color: rgba(255, 255, 255, 0.95); text-shadow: 0 1px 3px rgba(0, 0, 0, 0.2); line-height: 1.6;" data-aos="fade-up" data-aos-delay="200">分享矿业信息化领域的前沿观点和实践经验</p>
        </div>
    </section>
    
    <!-- 博客分类导航 -->
    <section style="background-color: var(--white); border-bottom: 1px solid #eee; padding: 20px 0;" data-aos="fade-up" data-aos-delay="300">
        <div class="container">
            <ul style="display: flex; flex-wrap: wrap; gap: 15px; list-style: none;">
                <li><a href="#" style="padding: 8px 15px; border-radius: 20px; background-color: var(--primary-color); color: var(--white); text-decoration: none; font-size: 0.9rem;">全部</a></li>
                <li><a href="#" style="padding: 8px 15px; border-radius: 20px; background-color: var(--light-gray); color: var(--primary-color); text-decoration: none; font-size: 0.9rem;">安全监测</a></li>
                <li><a href="#" style="padding: 8px 15px; border-radius: 20px; background-color: var(--light-gray); color: var(--primary-color); text-decoration: none; font-size: 0.9rem;">智能开采</a></li>
                <li><a href="#" style="padding: 8px 15px; border-radius: 20px; background-color: var(--light-gray); color: var(--primary-color); text-decoration: none; font-size: 0.9rem;">通风优化</a></li>
                <li><a href="#" style="padding: 8px 15px; border-radius: 20px; background-color: var(--light-gray); color: var(--primary-color); text-decoration: none; font-size: 0.9rem;">数字孪生</a></li>
                <li><a href="#" style="padding: 8px 15px; border-radius: 20px; background-color: var(--light-gray); color: var(--primary-color); text-decoration: none; font-size: 0.9rem;">智慧矿山</a></li>
                <li><a href="#" style="padding: 8px 15px; border-radius: 20px; background-color: var(--light-gray); color: var(--primary-color); text-decoration: none; font-size: 0.9rem;">技术前沿</a></li>
            </ul>
        </div>
    </section>
    
    <!-- 搜索栏 -->
    <section style="padding: 30px 0; background-color: var(--white);" data-aos="fade-up" data-aos-delay="400">
        <div class="container">
            <div style="max-width: 600px; margin: 0 auto;">
                <form style="display: flex; gap: 10px;">
                    <input type="text" placeholder="搜索关键词..." style="flex: 1; padding: 12px 15px; border: 1px solid #ddd; border-radius: 4px; font-family: var(--body-font);">
                    <button type="submit" style="background-color: var(--primary-color); color: var(--white); border: none; border-radius: 4px; padding: 0 20px; cursor: pointer;">
                        <i class="fas fa-search"></i>
                    </button>
                </form>
            </div>
        </div>
    </section>
    
    <!-- 博客文章列表 - 第3页 -->
    <section class="section">
        <div class="container">
            <!-- 最新文章 - 第3页 -->
            <div>
                <div style="margin-bottom: 2rem;" data-aos="fade-up" data-aos-delay="700">
                    <h2>全部文章 - 第3页</h2>
                </div>
                
                <div class="blog-grid">
                    <!-- 注意：这里可能内容较少，因为我们只有12-13篇文章，前两页已经展示了12篇 -->
                    <!-- 文章1 - 第3页 -->
                    <div class="blog-card" data-aos="fade-up" data-aos-delay="800">
                        <div class="blog-card-image">
                            <img src="../assets/images/blog/article-image-13.webp" alt="智能开采技术与传统开采方式的对比分析">
                        </div>
                        <div class="blog-card-content">
                            <span style="display: inline-block; padding: 5px 10px; background-color: var(--light-gray); color: var(--primary-color); font-size: 0.8rem; border-radius: 4px; margin-bottom: 10px;">智能开采</span>
                            <span class="blog-card-date">2022年12月10日</span>
                            <h3 class="blog-card-title">智能开采技术与传统开采方式的对比分析</h3>
                            <p class="blog-card-excerpt">智能化是煤矿未来发展的必然趋势。本文通过实际案例比较了智能开采技术与传统开采方式在安全性、效率和经济效益等方面的差异...</p>
                            <a href="intelligent-mining.html" class="btn btn-secondary">阅读全文</a>
                        </div>
                    </div>
                    
                    <!-- 归档说明 -->
                    <div style="grid-column: 1 / -1; text-align: center; padding: 40px 20px; background-color: var(--light-gray); border-radius: 8px;" data-aos="fade-up" data-aos-delay="900">
                        <i class="fas fa-archive" style="font-size: 3rem; color: var(--primary-color); margin-bottom: 20px;"></i>
                        <h3>更多历史文章正在整理中</h3>
                        <p>我们正在整理更多历史文章和技术资料，敬请期待！</p>
                        <a href="index.html" class="btn" style="margin-top: 20px;">返回最新文章</a>
                    </div>
                </div>
                
                <!-- 分页 -->
                <div style="margin-top: 4rem; text-align: center;" data-aos="fade-up" data-aos-delay="1100">
                    <ul style="display: inline-flex; list-style: none; gap: 5px;">
                        <li><a href="index.html" style="display: flex; align-items: center; justify-content: center; width: 40px; height: 40px; border-radius: 4px; background-color: var(--light-gray); color: var(--primary-color); text-decoration: none;">1</a></li>
                        <li><a href="page-2.html" style="display: flex; align-items: center; justify-content: center; width: 40px; height: 40px; border-radius: 4px; background-color: var(--light-gray); color: var(--primary-color); text-decoration: none;">2</a></li>
                        <li><a href="page-3.html" style="display: flex; align-items: center; justify-content: center; width: 40px; height: 40px; border-radius: 4px; background-color: var(--primary-color); color: var(--white); text-decoration: none;">3</a></li>
                        <li><span style="display: flex; align-items: center; justify-content: center; width: 40px; height: 40px; border-radius: 4px; background-color: var(--light-gray); color: var(--medium-gray); text-decoration: none; opacity: 0.5;"><i class="fas fa-chevron-right"></i></span></li>
                    </ul>
                </div>
            </div>
        </div>
    </section>
    
    <!-- 页脚 -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-logo">
                    <h3 style="color: var(--white);">张<span style="color: var(--secondary-color);">洁贞</span></h3>
                    <p>中矿天智信息科技(徐州)有限公司高级销售经理，专注矿山智能化解决方案。</p>
                    <div class="social-icons">
                        <a href="#" class="social-icon" title="抖音">
                            <img src="../assets/images/svg/douyin.svg" alt="抖音" style="width: 16px; height: 16px;">
                        </a>
                        <a href="#" class="social-icon" title="小红书">
                            <img src="../assets/images/svg/xiaohongshu.svg" alt="小红书" style="width: 16px; height: 16px;">
                        </a>
                        <a href="#" class="social-icon" title="微信">
                            <img src="../assets/images/svg/weixin.svg" alt="微信" style="width: 16px; height: 16px;">
                        </a>
                        <a href="#" class="social-icon" title="播客">
                            <img src="../assets/images/svg/boke.svg" alt="播客" style="width: 16px; height: 16px;">
                        </a>
                    </div>
                </div>
                
                <div class="footer-links">
                    <h4>网站导航</h4>
                    <ul>
                        <li><a href="../index.html">首页</a></li>
                        <li><a href="../about.html">关于我</a></li>
                        <li><a href="../blog/index.html">专业洞见</a></li>
                        <li><a href="../case-studies/index.html">成功案例</a></li>
                        <li><a href="../contact.html">联系方式</a></li>
                    </ul>
                </div>
                
                <div class="footer-links">
                    <h4>联系方式</h4>
                    <ul>
                        <li>
                            <img src="../assets/images/svg/email.svg" alt="邮箱" style="width: 16px; height: 16px; margin-right: 8px; vertical-align: middle;">
                            <a href="mailto:<EMAIL>"><EMAIL></a>
                        </li>
                        <li>
                            <img src="../assets/images/svg/dianhua.svg" alt="电话" style="width: 16px; height: 16px; margin-right: 8px; vertical-align: middle;">
                            <a href="tel:+8613938155869">+86 139 3815 5869</a>
                        </li>
                        <li>
                            <img src="../assets/images/svg/weixin.svg" alt="微信" style="width: 16px; height: 16px; margin-right: 8px; vertical-align: middle;">
                            <span style="color: var(--light-gray);">zhangjiezhen176</span>
                        </li>
                    </ul>
                </div>
            </div>
            
            <div class="footer-bottom">
                <p>&copy; 2025 张洁贞 - 矿业信息化解决方案专家</p>
            </div>
        </div>
    </footer>
    
    <!-- 移动导航菜单 -->
    <div class="mobile-nav-wrapper">
        <div class="mobile-nav">
            <button class="close-mobile-nav">
                <i class="fas fa-times"></i>
            </button>
            <ul>
                <li><a href="../index.html">首页</a></li>
                <li><a href="../about.html">关于我</a></li>
                <li><a href="../blog/index.html" class="active">洞见</a></li>
                <li><a href="../case-studies/index.html">案例</a></li>
                <li><a href="../contact.html">联系</a></li>
            </ul>
        </div>
        <div class="mobile-nav-backdrop"></div>
    </div>
    
    <!-- JavaScript -->
    <script src="../assets/js/main.js"></script>
    
    <!-- AOS初始化脚本 -->
    <script>
        // 页面加载进度条
        NProgress.configure({ 
            showSpinner: true,
            easing: 'ease',
            speed: 500
        });
        NProgress.start();
        
        document.addEventListener('DOMContentLoaded', function() {
            // 页面加载完成后结束进度条
            NProgress.done();
            
            // 初始化AOS动画库
            AOS.init({
                duration: 800,           // 动画持续时间
                easing: 'ease-in-out',   // 动画缓动函数
                once: true,              // 动画是否只播放一次
                mirror: false,           // 滚动向上时是否重新播放动画
                offset: 100              // 触发动画的位置偏移
            });
            
            // 移动导航菜单功能
            const mobileNavToggle = document.querySelector('.mobile-nav-toggle');
            const mobileNav = document.querySelector('.mobile-nav');
            const closeBtn = document.querySelector('.close-mobile-nav');
            const backdrop = document.querySelector('.mobile-nav-backdrop');
            
            // 打开导航菜单
            if (mobileNavToggle) {
                mobileNavToggle.addEventListener('click', function() {
                    document.body.classList.add('mobile-nav-active');
                    mobileNav.classList.add('active');
                    document.body.style.overflow = 'hidden'; // 防止背景滚动
                });
            }
            
            // 关闭导航菜单的几种方式
            if (closeBtn) {
                closeBtn.addEventListener('click', closeNav);
            }
            
            if (backdrop) {
                backdrop.addEventListener('click', closeNav);
            }
            
            // 点击导航链接后关闭菜单
            const mobileNavLinks = document.querySelectorAll('.mobile-nav a');
            mobileNavLinks.forEach(link => {
                link.addEventListener('click', closeNav);
            });
            
            function closeNav() {
                document.body.classList.remove('mobile-nav-active');
                mobileNav.classList.remove('active');
                document.body.style.overflow = 'auto'; // 允许背景滚动
            }
            
            // 导航栏滚动效果
            const header = document.querySelector('.header');
            window.addEventListener('scroll', function() {
                if (window.scrollY > 100) {
                    header.classList.add('scrolled');
                } else {
                    header.classList.remove('scrolled');
                }
            });
            
            // 搜索表单处理
            const searchForm = document.querySelector('form');
            if (searchForm) {
                searchForm.addEventListener('submit', function(e) {
                    e.preventDefault();
                    const searchInput = this.querySelector('input[type="text"]');
                    if (!searchInput.value.trim()) {
                        alert('请输入搜索关键词');
                        return;
                    }
                    
                    // 模拟搜索功能
                    alert('正在搜索: ' + searchInput.value);
                    // 实际项目中可以跳转到搜索结果页面
                });
            }
        });
    </script>
</body>
</html> 