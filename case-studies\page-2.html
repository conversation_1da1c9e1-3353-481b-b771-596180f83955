<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="查看矿业信息化领域专家的成功案例，了解如何通过智能化解决方案实现煤矿安全高效生产">
    <title>成功案例研究 - 第2页 | 张洁贞的矿业信息化解决方案</title>
    <link rel="stylesheet" href="../assets/css/main.css">
    <link rel="stylesheet" href="../assets/css/responsive.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <!-- AOS动画库 -->
    <link rel="stylesheet" href="https://cdn.bootcdn.net/ajax/libs/aos/2.3.4/aos.css">
    <script src="https://cdn.bootcdn.net/ajax/libs/aos/2.3.4/aos.js" defer></script>
    <!-- NProgress加载进度条 -->
    <link rel="stylesheet" href="https://cdn.bootcdn.net/ajax/libs/nprogress/0.2.0/nprogress.min.css">
    <script src="https://cdn.bootcdn.net/ajax/libs/nprogress/0.2.0/nprogress.min.js"></script>
    <!-- Swiper轮播库 -->
    <link rel="stylesheet" href="https://cdn.bootcdn.net/ajax/libs/Swiper/8.4.5/swiper-bundle.min.css">
    <script src="https://cdn.bootcdn.net/ajax/libs/Swiper/8.4.5/swiper-bundle.min.js" defer></script>
    <style>
        /* 自定义NProgress样式 */
        #nprogress .bar {
            background: var(--primary-color); /* 使用网站主色调 */
            height: 3px;
        }
        #nprogress .peg {
            box-shadow: 0 0 10px var(--primary-color), 0 0 5px var(--primary-color);
        }
        #nprogress .spinner-icon {
            border-top-color: var(--primary-color);
            border-left-color: var(--primary-color);
        }
        
        /* Swiper客户评价轮播样式 */
        .testimonial-swiper {
            padding-bottom: 50px;
        }
        .swiper-pagination-bullet {
            width: 10px;
            height: 10px;
            background-color: #ccc;
            opacity: 0.5;
        }
        .swiper-pagination-bullet-active {
            background-color: var(--primary-color);
            opacity: 1;
        }
        .swiper-button-next, .swiper-button-prev {
            color: var(--primary-color);
        }
        .testimonial {
            padding: 20px;
            transition: transform 0.3s;
        }
        
        /* 按钮悬停效果 */
        .btn {
            transition: all 0.3s ease-in-out;
            position: relative;
            overflow: hidden;
        }
        .btn:after {
            content: '';
            position: absolute;
            width: 0;
            height: 100%;
            top: 0;
            left: 0;
            background: rgba(255, 255, 255, 0.1);
            transition: width 0.3s ease;
        }
        .btn:hover:after {
            width: 100%;
        }
        .btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
        }
        
        /* 案例卡片悬停效果 */
        .case-card {
            transition: all 0.3s ease-in-out;
        }
        .case-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
        }
        
        /* 导航链接悬停效果 */
        .nav-link {
            position: relative;
            overflow: hidden;
        }
        .nav-link:before {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 0;
            height: 2px;
            background-color: var(--primary-color);
            transition: width 0.3s ease;
        }
        .nav-link:hover:before {
            width: 100%;
        }
        
        /* 阅读更多链接悬停效果 */
        .read-more {
            transition: all 0.3s ease;
        }
        .read-more:hover {
            color: var(--primary-color);
            padding-left: 5px;
        }
        .read-more i {
            transition: transform 0.3s ease;
        }
        .read-more:hover i {
            transform: translateX(5px);
        }
    </style>
</head>
<body>
    <!-- 顶部导航栏 -->
    <header class="header">
        <div class="container">
            <nav class="navbar">
                <a href="../index.html" class="logo">张<span>洁贞</span><span class="logo-subtitle">矿业信息化专家</span></a>
                <ul class="nav-menu">
                    <li class="nav-item"><a href="../index.html" class="nav-link">首页</a></li>
                    <li class="nav-item"><a href="../about.html" class="nav-link">关于我</a></li>
                    <li class="nav-item"><a href="../blog/index.html" class="nav-link">洞见</a></li>
                    <li class="nav-item"><a href="../case-studies/index.html" class="nav-link active">案例</a></li>
                    <li class="nav-item"><a href="../contact.html" class="nav-link">联系</a></li>
                    <li class="nav-item"><a href="../contact.html" class="nav-contact-btn"><i class="fas fa-headset"></i>咨询我</a></li>
                </ul>
                <button class="mobile-nav-toggle">
                    <i class="fas fa-bars"></i>
                </button>
            </nav>
        </div>
    </header>
    
    <!-- 页面标题部分 -->
    <section class="page-title" style="background-image: url('../assets/images/case-studies-bg.webp');" data-aos="fade-in" data-aos-duration="1000">
        <div class="overlay"></div>
        <div class="container">
            <h1 data-aos="fade-up" data-aos-delay="200">成功案例研究 - 第2页</h1>
            <p data-aos="fade-up" data-aos-delay="300">深入了解我如何帮助煤矿企业实现安全高效生产，通过智能化技术解决矿山生产挑战</p>
        </div>
    </section>

    <!-- 案例研究筛选器 -->
    <section class="case-filter" data-aos="fade-up" data-aos-duration="800">
        <div class="container">
            <div class="filter-wrapper">
                <div class="filter-group">
                    <label>按矿区类型筛选：</label>
                    <select id="industry-filter">
                        <option value="all">全部矿区</option>
                        <option value="underground">井工开采</option>
                        <option value="openpit">露天开采</option>
                        <option value="coal">煤矿</option>
                        <option value="metal">金属矿</option>
                        <option value="nonmetal">非金属矿</option>
                    </select>
                </div>
                <div class="filter-group">
                    <label>按解决方案类型：</label>
                    <select id="challenge-filter">
                        <option value="all">全部类型</option>
                        <option value="safety">安全监测</option>
                        <option value="intelligent-mining">智能开采</option>
                        <option value="ventilation">通风优化</option>
                        <option value="production">生产调度</option>
                        <option value="digital-twin">数字孪生</option>
                    </select>
                </div>
                <div class="search-box">
                    <input type="text" placeholder="搜索案例关键词...">
                    <button><i class="fas fa-search"></i></button>
                </div>
            </div>
        </div>
    </section>

    <!-- 案例研究列表 -->
    <section class="case-studies-list">
        <div class="container">
            <!-- 案例研究网格 - 第2页 -->
            <div class="case-grid">
                <!-- 案例1 - 第2页 -->
                <div class="case-card" data-industry="coal" data-challenge="intelligent-mining" data-aos="fade-up" data-aos-delay="100">
                    <div class="case-image">
                        <img src="../assets/images/blog/article-thumbnail-6.webp" alt="5G+AI技术在煤矿智能巡检中的创新应用">
                    </div>
                    <div class="case-content">
                        <div class="case-category">技术前沿 · 井工煤矿</div>
                        <h3>5G+AI技术在煤矿智能巡检中的创新应用</h3>
                        <p>利用5G网络高带宽、低延时特性，结合AI视觉识别技术，打造了无人智能巡检系统，实现了危险环境下的智能巡检，提高了安全监测效率和准确性。</p>
                        <a href="case-5g-ai-inspection.html" class="read-more">查看案例 <i class="fas fa-arrow-right"></i></a>
                    </div>
                </div>

                <!-- 更多案例说明 -->
                <div style="grid-column: 1 / -1; text-align: center; padding: 40px 20px; background-color: var(--light-gray); border-radius: 8px; margin-top: 20px;" data-aos="fade-up" data-aos-delay="200">
                    <i class="fas fa-folder-open" style="font-size: 3rem; color: var(--primary-color); margin-bottom: 20px;"></i>
                    <h3>更多案例正在整理中</h3>
                    <p>我们正在整理更多矿山信息化解决方案的实际应用案例，敬请期待！</p>
                    <a href="index.html" class="btn" style="margin-top: 20px;">返回案例首页</a>
                </div>
            </div>

            <!-- 分页导航 -->
            <div style="margin-top: 4rem; text-align: center;" data-aos="fade-up" data-aos-delay="400">
                <ul style="display: inline-flex; list-style: none; gap: 5px;">
                    <li><a href="index.html" style="display: flex; align-items: center; justify-content: center; width: 40px; height: 40px; border-radius: 4px; background-color: var(--light-gray); color: var(--primary-color); text-decoration: none;">1</a></li>
                    <li><a href="page-2.html" style="display: flex; align-items: center; justify-content: center; width: 40px; height: 40px; border-radius: 4px; background-color: var(--primary-color); color: var(--white); text-decoration: none;">2</a></li>
                    <li><span style="display: flex; align-items: center; justify-content: center; width: 40px; height: 40px; border-radius: 4px; background-color: var(--light-gray); color: var(--medium-gray); text-decoration: none; opacity: 0.5;"><i class="fas fa-chevron-right"></i></span></li>
                </ul>
            </div>
        </div>
    </section>

    <!-- 客户见证 -->
    <section class="client-testimonials" data-aos="fade-up" data-aos-duration="800">
        <div class="container">
            <h2>客户评价</h2>
            <div class="testimonial-swiper">
                <div class="swiper-wrapper">
                    <div class="swiper-slide">
                        <div class="testimonial">
                            <div class="testimonial-content">
                                <p>"张洁贞的专业素养和技术理解力给我们留下了深刻印象。她不仅精通各种矿山信息化技术，更能结合我们矿区的实际情况，提供切实可行的解决方案。在她的帮助下，我们矿井安全生产水平得到了显著提升。"</p>
                            </div>
                            <div class="testimonial-author">
                                <img src="../assets/images/testimonials/client1.webp" alt="王总工照片">
                                <div class="author-info">
                                    <h4>王志远</h4>
                                    <p>陕西某煤矿集团总工程师</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="swiper-slide">
                        <div class="testimonial">
                            <div class="testimonial-content">
                                <p>"与张洁贞和中矿天智合作是一次关键的决策。她对矿井安全生产的理解和专业知识帮助我们建立了先进的安全监测系统，显著降低了安全事故率。她的方案不仅解决了当前问题，更为我们的长期发展奠定了基础。"</p>
                            </div>
                            <div class="testimonial-author">
                                <img src="../assets/images/testimonials/client2.webp" alt="李经理照片">
                                <div class="author-info">
                                    <h4>李梦华</h4>
                                    <p>山西某煤业公司安全经理</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- 添加额外的客户评价 -->
                    <div class="swiper-slide">
                        <div class="testimonial">
                            <div class="testimonial-content">
                                <p>"张洁贞提供的矿井通风优化解决方案为我们节省了大量能源成本，同时提高了通风效率。她专业、高效的工作态度和持续的技术支持让我们非常满意。"</p>
                            </div>
                            <div class="testimonial-author">
                                <img src="../assets/images/testimonials/client3.webp" alt="张经理照片">
                                <div class="author-info">
                                    <h4>张明辉</h4>
                                    <p>山东某矿业集团生产经理</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- 添加第四条客户评价 -->
                    <div class="swiper-slide">
                        <div class="testimonial">
                            <div class="testimonial-content">
                                <p>"与张洁贞合作开发智能调度平台是我们企业数字化转型的重要一步。她对业务流程的深刻理解和信息化系统的专业知识，帮助我们建立了高效、可视化的生产调度体系，大幅提升了协同效率。"</p>
                            </div>
                            <div class="testimonial-author">
                                <img src="../assets/images/testimonials/client4.webp" alt="赵主管照片">
                                <div class="author-info">
                                    <h4>赵建国</h4>
                                    <p>内蒙古某煤矿信息化主管</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- 添加分页器和导航按钮 -->
                <div class="swiper-pagination"></div>
                <div class="swiper-button-prev"></div>
                <div class="swiper-button-next"></div>
            </div>
        </div>
    </section>

    <!-- 行动召唤 -->
    <section class="cta-section" data-aos="zoom-in" data-aos-duration="800">
        <div class="container">
            <div class="cta-content">
                <h2>想了解我如何帮助您解决矿山信息化挑战？</h2>
                <p>无论您面临的是安全生产、智能开采还是数字化转型方面的挑战，我都可以提供专业解决方案。</p>
                <a href="../contact.html" class="btn btn-light">预约咨询</a>
            </div>
        </div>
    </section>

    <!-- 页脚 -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-logo">
                    <h3 style="color: var(--white);">张<span style="color: var(--secondary-color);">洁贞</span></h3>
                    <p>中矿天智信息科技(徐州)有限公司高级销售经理，专注矿山智能化解决方案。</p>
                    <div class="social-icons">
                        <a href="#" class="social-icon" title="抖音">
                            <img src="../assets/images/svg/douyin.svg" alt="抖音" style="width: 16px; height: 16px;">
                        </a>
                        <a href="#" class="social-icon" title="小红书">
                            <img src="../assets/images/svg/xiaohongshu.svg" alt="小红书" style="width: 16px; height: 16px;">
                        </a>
                        <a href="#" class="social-icon" title="微信">
                            <img src="../assets/images/svg/weixin.svg" alt="微信" style="width: 16px; height: 16px;">
                        </a>
                        <a href="#" class="social-icon" title="播客">
                            <img src="../assets/images/svg/boke.svg" alt="播客" style="width: 16px; height: 16px;">
                        </a>
                    </div>
                </div>
                
                <div class="footer-links">
                    <h4>网站导航</h4>
                    <ul>
                        <li><a href="../index.html">首页</a></li>
                        <li><a href="../about.html">关于我</a></li>
                        <li><a href="../blog/index.html">专业洞见</a></li>
                        <li><a href="index.html">成功案例</a></li>
                        <li><a href="../contact.html">联系方式</a></li>
                    </ul>
                </div>
                
                <div class="footer-links">
                    <h4>联系方式</h4>
                    <ul>
                        <li>
                            <img src="../assets/images/svg/email.svg" alt="邮箱" style="width: 16px; height: 16px; margin-right: 8px; vertical-align: middle;">
                            <a href="mailto:<EMAIL>"><EMAIL></a>
                        </li>
                        <li>
                            <img src="../assets/images/svg/dianhua.svg" alt="电话" style="width: 16px; height: 16px; margin-right: 8px; vertical-align: middle;">
                            <a href="tel:+8613938155869">+86 139 3815 5869</a>
                        </li>
                        <li>
                            <img src="../assets/images/svg/weixin.svg" alt="微信" style="width: 16px; height: 16px; margin-right: 8px; vertical-align: middle;">
                            <span style="color: var(--light-gray);">zhangjiezhen176</span>
                        </li>
                    </ul>
                </div>
            </div>
            
            <div class="footer-bottom">
                <p>© 2025 张洁贞 - 矿业信息化解决方案专家</p>
            </div>
        </div>
    </footer>

    <!-- JavaScript -->
    <script>
        // 页面加载进度条
        NProgress.configure({ 
            showSpinner: true,
            easing: 'ease',
            speed: 500
        });
        NProgress.start();
        
        document.addEventListener('DOMContentLoaded', function() {
            // 页面加载完成后结束进度条
            NProgress.done();
            
            // 初始化AOS动画库
            AOS.init({
                duration: 800,           // 动画持续时间
                easing: 'ease-in-out',   // 动画缓动函数
                once: true,              // 动画是否只播放一次
                mirror: false,           // 滚动向上时是否重新播放动画
                offset: 100              // 触发动画的位置偏移
            });
            
            // 初始化Swiper客户评价轮播
            new Swiper('.testimonial-swiper', {
                slidesPerView: 1,
                spaceBetween: 30,
                loop: true,
                autoplay: {
                    delay: 5000,
                    disableOnInteraction: false,
                },
                pagination: {
                    el: '.swiper-pagination',
                    clickable: true,
                },
                navigation: {
                    nextEl: '.swiper-button-next',
                    prevEl: '.swiper-button-prev',
                },
                effect: 'fade',
                fadeEffect: {
                    crossFade: true
                },
                speed: 800
            });
            
            // 创建移动导航元素
            const mobileNavHTML = `
                <div class="mobile-nav">
                    <button class="close-mobile-nav">
                        <i class="fas fa-times"></i>
                    </button>
                    <ul>
                        <li><a href="../index.html">首页</a></li>
                        <li><a href="../about.html">关于我</a></li>
                        <li><a href="../blog/index.html">洞见</a></li>
                        <li><a href="index.html" class="active">案例</a></li>
                        <li><a href="../contact.html">联系</a></li>
                    </ul>
                </div>
                <div class="mobile-nav-backdrop"></div>
            `;
            
            // 检查移动导航菜单是否已存在
            if (!document.querySelector('.mobile-nav-wrapper')) {
                const mobileNavWrapper = document.createElement('div');
                mobileNavWrapper.className = 'mobile-nav-wrapper';
                mobileNavWrapper.innerHTML = mobileNavHTML;
                document.body.appendChild(mobileNavWrapper);
            }
            
            // 获取DOM元素
            const header = document.querySelector('.header');
            const mobileNavToggle = document.querySelector('.mobile-nav-toggle');
            const mobileNav = document.querySelector('.mobile-nav');
            const closeBtn = document.querySelector('.close-mobile-nav');
            const backdrop = document.querySelector('.mobile-nav-backdrop');
            const currentPath = window.location.pathname;
            
            // 设置当前页面的活动链接（桌面导航）
            const navLinks = document.querySelectorAll('.nav-link');
            navLinks.forEach(link => {
                const href = link.getAttribute('href');
                const fullPath = new URL(href, window.location.origin).pathname;
                
                // 精确匹配当前路径
                if (currentPath === fullPath || 
                    (currentPath.endsWith('/case-studies/') && href.includes('/case-studies/index.html')) ||
                    (currentPath.endsWith('/case-studies/index.html') && href.includes('/case-studies/index.html')) ||
                    (currentPath.endsWith('/case-studies/page-2.html') && href.includes('/case-studies/index.html'))) {
                    link.classList.add('active');
                } else {
                    link.classList.remove('active');
                }
            });
            
            // 打开导航菜单
            if (mobileNavToggle) {
                mobileNavToggle.addEventListener('click', function() {
                    document.body.classList.add('mobile-nav-active');
                    mobileNav.classList.add('active');
                    document.body.style.overflow = 'hidden'; // 防止背景滚动
                });
            }
            
            // 关闭导航菜单的几种方式
            if (closeBtn) {
                closeBtn.addEventListener('click', closeNav);
            }
            
            if (backdrop) {
                backdrop.addEventListener('click', closeNav);
            }
            
            // 点击导航链接后关闭菜单
            const mobileNavLinks = document.querySelectorAll('.mobile-nav a');
            mobileNavLinks.forEach(link => {
                link.addEventListener('click', function() {
                    closeNav();
                });
            });
            
            // 设置当前页面的活动链接（移动导航）
            mobileNavLinks.forEach(link => {
                const href = link.getAttribute('href');
                const fullPath = new URL(href, window.location.origin).pathname;
                
                // 精确匹配当前路径
                if (currentPath === fullPath || 
                    (currentPath.endsWith('/case-studies/') && href.includes('/case-studies/index.html')) ||
                    (currentPath.endsWith('/case-studies/index.html') && href.includes('/case-studies/index.html')) ||
                    (currentPath.endsWith('/case-studies/page-2.html') && href.includes('/case-studies/index.html'))) {
                    link.classList.add('active');
                } else {
                    link.classList.remove('active');
                }
            });
            
            // 滚动时导航栏样式变化
            window.addEventListener('scroll', function() {
                if (window.scrollY > 100) {
                    header.style.padding = '10px 0';
                    header.style.boxShadow = '0 2px 10px rgba(0, 0, 0, 0.1)';
                } else {
                    header.style.padding = '15px 0';
                    header.style.boxShadow = '0 2px 10px rgba(0, 0, 0, 0.05)';
                }
            });
            
            // 分页功能及筛选功能逻辑
            const filterIndustry = document.getElementById('industry-filter');
            const filterChallenge = document.getElementById('challenge-filter');
            const caseCards = document.querySelectorAll('.case-card');

            if (filterIndustry && filterChallenge) {
                // 筛选变化时更新显示
                filterIndustry.addEventListener('change', updateFilters);
                filterChallenge.addEventListener('change', updateFilters);
                
                // 筛选功能
                function updateFilters() {
                    const industryValue = filterIndustry.value;
                    const challengeValue = filterChallenge.value;
                    
                    caseCards.forEach(card => {
                        const industryMatch = industryValue === 'all' || card.dataset.industry === industryValue;
                        const challengeMatch = challengeValue === 'all' || card.dataset.challenge === challengeValue;
                        
                        if (industryMatch && challengeMatch) {
                            card.style.display = 'block';
                        } else {
                            card.style.display = 'none';
                        }
                    });
                }
            }
        });

        function closeNav() {
            document.body.classList.remove('mobile-nav-active');
            document.querySelector('.mobile-nav').classList.remove('active');
            document.body.style.overflow = 'auto'; // 允许背景滚动
        }
    </script>
</body>
</html> 