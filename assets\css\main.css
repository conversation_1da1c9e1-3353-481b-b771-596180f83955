/* 引入性能优化CSS */
@import url('performance.css');

/* 
 * 矿业信息化解决方案专家个人品牌网站
 * 主样式表
 */

/* 使用本地托管的字体文件 */
@font-face {
  font-family: 'Noto Sans SC';
  font-style: normal;
  font-weight: 300;
  src: url('../fonts/noto-sans-sc-300.woff2') format('woff2');
  font-display: swap;
}

@font-face {
  font-family: 'Noto Sans SC';
  font-style: normal;
  font-weight: 400;
  src: url('../fonts/noto-sans-sc-regular.woff2') format('woff2');
  font-display: swap;
}

@font-face {
  font-family: 'Noto Sans SC';
  font-style: normal;
  font-weight: 500;
  src: url('../fonts/noto-sans-sc-500.woff2') format('woff2');
  font-display: swap;
}

@font-face {
  font-family: 'Noto Sans SC';
  font-style: normal;
  font-weight: 600;
  src: url('../fonts/noto-sans-sc-600.woff2') format('woff2');
  font-display: swap;
}

@font-face {
  font-family: 'Noto Sans SC';
  font-style: normal;
  font-weight: 700;
  src: url('../fonts/noto-sans-sc-700.woff2') format('woff2');
  font-display: swap;
}

@font-face {
  font-family: 'Source Serif Pro';
  font-style: normal;
  font-weight: 400;
  src: url('../fonts/source-serif-pro-regular.woff2') format('woff2');
  font-display: swap;
}

@font-face {
  font-family: 'Source Serif Pro';
  font-style: normal;
  font-weight: 600;
  src: url('../fonts/source-serif-pro-600.woff2') format('woff2');
  font-display: swap;
}

@font-face {
  font-family: 'Source Serif Pro';
  font-style: normal;
  font-weight: 700;
  src: url('../fonts/source-serif-pro-700.woff2') format('woff2');
  font-display: swap;
}

@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 300;
  src: url('../fonts/roboto-300.woff2') format('woff2');
  font-display: swap;
}

@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 400;
  src: url('../fonts/roboto-regular.woff2') format('woff2');
  font-display: swap;
}

@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 500;
  src: url('../fonts/roboto-500.woff2') format('woff2');
  font-display: swap;
}

@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 700;
  src: url('../fonts/roboto-700.woff2') format('woff2');
  font-display: swap;
}

/* 基础样式重置优化 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  -webkit-tap-highlight-color: transparent;
}

/* 性能优化 - 减少重排重绘 */
html {
  scrollbar-width: thin;
  scroll-behavior: smooth;
  text-size-adjust: 100%;
  -webkit-text-size-adjust: 100%;
}

body {
  font-family: var(--body-font);
  color: var(--dark-gray);
  line-height: 1.6;
  background-color: var(--white);
  overflow-x: hidden;
  width: 100%;
  position: relative;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

:root {
  /* 定义配色方案 - 矿业科技感 */
  --primary-color: #1A365D;      /* 深海蓝 - 主色调 */
  --secondary-color: #D4AF37;    /* 金色 - 辅助色 */
  --accent-color: #4A8FB5;       /* 科技蓝 - 强调色 */
  --tech-gray: #546A7B;          /* 科技灰 - 辅助色 */
  --white: #FFFFFF;
  --light-gray: #F5F5F5;
  --medium-gray: #8A9CAA;        /* 更新为冷调灰色 */
  --dark-gray: #333333;
  
  /* 定义字体 */
  --title-font: 'Source Serif Pro', serif, 'PingFang SC', 'Microsoft YaHei';   /* 专业感标题字体 */
  --body-font: 'Noto Sans SC', system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'PingFang SC', 'Microsoft YaHei', sans-serif;  /* 现代感正文字体 */
  --accent-font: 'Roboto', 'Noto Sans SC', system-ui, -apple-system, 'Segoe UI', 'PingFang SC', sans-serif;  /* 强调字体 */
  
  /* 定义间距 */
  --spacing-xs: 0.5rem;
  --spacing-sm: 1rem;
  --spacing-md: 2rem;
  --spacing-lg: 3rem;
  --spacing-xl: 5rem;
  
  /* 定义过渡时间 */
  --transition-fast: 0.2s;
  --transition-normal: 0.3s;
  --transition-slow: 0.5s;
  
  /* 定义圆角 */
  --radius-sm: 4px;
  --radius-md: 8px;
  --radius-lg: 16px;
  
  /* 定义阴影 */
  --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.1);
  --shadow-md: 0 4px 8px rgba(0, 0, 0, 0.12);
  --shadow-lg: 0 8px 16px rgba(0, 0, 0, 0.15);
}

h1, h2, h3, h4, h5, h6 {
  font-family: var(--title-font);
  font-weight: 600;
  color: var(--primary-color);
  margin-bottom: 1rem;
}

h1 {
  font-size: 3.5rem;
  font-weight: 700;
  letter-spacing: -0.5px;
}

h2 {
  font-size: 2.5rem;
  position: relative;
  margin-bottom: var(--spacing-lg);
  letter-spacing: -0.3px;
}

h2::after {
  content: '';
  position: absolute;
  bottom: -15px;
  left: 50%;
  transform: translateX(-50%);
  width: auto;
  min-width: 80px;
  height: 3px;
  background-color: var(--secondary-color);
  transition: width var(--transition-normal) ease;
}

/* 无装饰的标题 */
h2.no-decoration::after {
  display: none;
}

h3 {
  font-size: 1.8rem;
  letter-spacing: -0.2px;
}

h4 {
  font-size: 1.5rem;
  letter-spacing: -0.1px;
}

p {
  margin-bottom: var(--spacing-md);
  font-size: 1.1rem;
  line-height: 1.7;
}

a {
  color: var(--primary-color);
  text-decoration: none;
  transition: all var(--transition-normal) ease;
  position: relative;
}

a:hover {
  color: var(--secondary-color);
}

blockquote {
  font-family: var(--accent-font);
  font-style: italic;
  font-size: 1.3rem;
  color: var(--primary-color);
  border-left: 3px solid var(--secondary-color);
  padding-left: var(--spacing-md);
  margin: var(--spacing-lg) 0;
  background-color: rgba(26, 54, 93, 0.03);
  padding: var(--spacing-md);
  border-radius: 0 var(--radius-md) var(--radius-md) 0;
}

/* 通用布局 */
.container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--spacing-md);
}

.section {
  padding: var(--spacing-xl) 0;
}

.section-title {
  text-align: center;
  margin-bottom: var(--spacing-lg);
}

.section-title h2::after {
  left: 50%;
  transform: translateX(-50%);
  width: auto;
  min-width: 80px;
}

/* 按钮样式 */
.btn {
  display: inline-block;
  padding: 12px 30px;
  background-color: var(--primary-color);
  color: var(--white);
  border: none;
  border-radius: var(--radius-sm);
  font-family: var(--body-font);
  font-weight: 500;
  cursor: pointer;
  transition: all var(--transition-normal) ease;
  text-transform: uppercase;
  letter-spacing: 1px;
  font-size: 0.9rem;
  box-shadow: var(--shadow-sm);
}

.btn:hover {
  background-color: var(--secondary-color);
  color: var(--dark-gray);
  box-shadow: var(--shadow-md);
  transform: translateY(-2px);
}

.btn-secondary {
  background-color: transparent;
  border: 2px solid var(--primary-color);
  color: var(--primary-color);
}

.btn-secondary:hover {
  background-color: var(--primary-color);
  color: var(--white);
  transform: translateY(-2px);
}

/* 关于页面的查看成功案例按钮 */
.about-case-btn {
  background-color: transparent;
  border: 2px solid var(--white);
  color: var(--white);
  padding: 10px 20px;
  border-radius: 4px;
  cursor: pointer;
  transition: all var(--transition-normal) ease;
  text-transform: uppercase;
  letter-spacing: 1px;
  font-size: 0.9rem;
  box-shadow: var(--shadow-sm);
}

.about-case-btn:hover {
  background-color: var(--white);
  color: var(--primary-color);
  box-shadow: var(--shadow-md);
  transform: translateY(-2px);
}

/* 导航栏 */
.header {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  background-color: var(--white);
  z-index: 1000;
  transform: translateZ(0);
  will-change: transform, opacity;
  transition: transform var(--transition-normal) cubic-bezier(0.4, 0, 0.2, 1),
              background-color var(--transition-normal) ease;
  box-shadow: var(--shadow-sm);
  padding: var(--spacing-sm) 0;
}

.header.scrolled {
  background-color: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  box-shadow: var(--shadow-md);
  padding: 8px 0;
}

.navbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.logo {
  font-family: var(--title-font);
  font-size: 1.8rem;
  font-weight: 700;
  color: var(--primary-color);
  transition: all var(--transition-normal) ease;
  position: relative;
  display: flex;
  align-items: center;
  text-decoration: none;
  letter-spacing: 0.5px;
}

.logo span {
  color: var(--secondary-color);
  transition: all var(--transition-normal) ease;
  position: relative;
}

.logo::before {
  content: '';
  position: absolute;
  width: 8px;
  height: 8px;
  background-color: var(--accent-color);
  border-radius: 50%;
  left: -12px;
  top: 50%;
  transform: translateY(-50%);
  opacity: 0;
  transition: all var(--transition-normal) ease;
  box-shadow: 0 0 8px var(--accent-color);
}

/* 添加更明显的焦点和专业感 */
.logo span::after {
  content: '';
  position: absolute;
  width: 100%;
  height: 3px;
  background-color: var(--secondary-color);
  bottom: -3px;
  left: 0;
  transform: scaleX(0);
  transform-origin: left;
  transition: transform var(--transition-normal) cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

.logo:hover {
  transform: translateY(-2px);
}

.logo:hover::before {
  opacity: 1;
  left: -15px;
  width: 10px;
  height: 10px;
}

.logo:hover span {
  color: var(--accent-color);
}

.logo:hover span::after {
  transform: scaleX(1);
}

.logo-subtitle {
  display: none;
  font-size: 0.7rem;
  color: var(--tech-gray);
  margin-left: 10px;
  font-weight: normal;
  letter-spacing: 0.7px;
  text-transform: uppercase;
  border-left: 1px solid var(--light-gray);
  padding-left: 10px;
  transition: all var(--transition-normal) ease;
  background: linear-gradient(to right, var(--tech-gray), var(--primary-color));
  background-clip: text;
  -webkit-background-clip: text;
  color: transparent;
}

@media (min-width: 768px) {
  .logo-subtitle {
    display: block;
  }
}

.logo:hover .logo-subtitle {
  transform: translateX(3px);
}

.nav-menu {
  display: flex;
  list-style: none;
  margin: 0;
  padding: 0;
}

.nav-item {
  margin-left: var(--spacing-sm);
  position: relative;
}

.nav-link {
  font-size: 1rem;
  padding: 10px 12px;
  position: relative;
  font-weight: 500;
  color: var(--primary-color);
  display: inline-block;
  transition: all var(--transition-normal) ease;
}

.nav-link::after {
  content: '';
  position: absolute;
  width: 0;
  height: 2px;
  bottom: 0;
  left: 50%;
  background-color: var(--secondary-color);
  transition: all var(--transition-normal) cubic-bezier(0.68, -0.55, 0.265, 1.55);
  transform: translateX(-50%);
  opacity: 0;
}

.nav-link:hover {
  color: var(--accent-color);
}

.nav-link:hover::after,
.nav-link.active::after {
  width: 80%;
  opacity: 1;
}

.nav-link.active {
  color: var(--accent-color);
  font-weight: 600;
}

/* 导航附加元素 - 联系按钮 */
.nav-contact-btn {
  margin-left: var(--spacing-md);
  padding: 8px 15px;
  background-color: rgba(26, 54, 93, 0.1);
  color: var(--primary-color);
  border-radius: 30px;
  font-size: 0.9rem;
  font-weight: 500;
  transition: all var(--transition-normal) ease;
  display: inline-flex;
  align-items: center;
  border: 1px solid transparent;
}

.nav-contact-btn i {
  margin-right: 6px;
  font-size: 0.8rem;
}

.nav-contact-btn:hover {
  background-color: var(--primary-color);
  color: var(--white);
  transform: translateY(-2px);
  box-shadow: var(--shadow-sm);
}

/* 导航栏背景特效 */
.header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 3px;
  background: linear-gradient(90deg, var(--primary-color), var(--accent-color), var(--secondary-color));
  opacity: 0;
  transition: opacity var(--transition-normal) ease;
}

.header.scrolled::before {
  opacity: 1;
}

/* 主页 Hero Section */
.hero {
  height: 80vh;
  min-height: 500px;
  display: flex;
  align-items: center;
  color: var(--white);
  padding: var(--spacing-lg) 0;
  margin-top: 64px; /* 导航栏高度 */
  position: relative;
  overflow: hidden;
}

.hero::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(26, 54, 93, 0.85), rgba(21, 32, 50, 0.95));
  z-index: 1;
}

/* 添加动态网格背景效果 */
.hero::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: 
    linear-gradient(rgba(255, 255, 255, 0.05) 1px, transparent 1px),
    linear-gradient(90deg, rgba(255, 255, 255, 0.05) 1px, transparent 1px);
  background-size: 20px 20px;
  z-index: 2;
  opacity: 0.4;
}

.hero-content {
  position: relative;
  z-index: 3;
  max-width: 800px;
  animation: fadeInUp 1s ease-out forwards;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.hero-title {
  font-size: 4rem;
  margin-bottom: var(--spacing-sm);
  line-height: 1.1;
  text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
  position: relative;
  display: inline-block;
}

.hero-title::after {
  content: '';
  position: absolute;
  bottom: -10px;
  left: 0;
  width: 80px;
  height: 4px;
  background: linear-gradient(to right, var(--secondary-color), var(--accent-color));
  border-radius: 2px;
}

.hero-subtitle {
  font-size: 1.5rem;
  margin-bottom: var(--spacing-xl);
  font-weight: 400;
  line-height: 1.4;
  text-shadow: 0 1px 5px rgba(0, 0, 0, 0.2);
  max-width: 90%;
  opacity: 0;
  animation: fadeIn 1s ease-out 0.5s forwards;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.hero-cta {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  opacity: 0;
  animation: fadeIn 1s ease-out 0.8s forwards;
}

.hero-cta .btn {
  padding: 12px 25px;
  font-weight: 500;
  letter-spacing: 0.5px;
  transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

.hero-cta .btn:hover {
  transform: translateY(-3px) scale(1.05);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.15);
}

/* 添加波浪装饰元素 */
.hero-wave {
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 100%;
  z-index: 4;
  transform: translateY(50%);
}

.hero-wave svg {
  display: block;
  width: 100%;
  height: auto;
  fill: var(--white);
}

/* 关于我部分 */
.about-section {
  background-color: var(--white);
}

.about-content {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-lg);
  align-items: center;
}

.about-image {
  flex: 1;
  min-width: 300px;
  display: flex;
  justify-content: center;
}

.about-image img {
  width: 100%;
  height: auto;
  max-width: 600px;
  object-fit: contain;
  border-radius: 4px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.about-text {
  flex: 2;
  min-width: 300px;
}

/* 博客文章卡片 */
.blog-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: var(--spacing-md);
}

.blog-card {
  background-color: var(--white);
  border-radius: var(--radius-md);
  overflow: hidden;
  box-shadow: var(--shadow-sm);
  transition: all var(--transition-normal) ease;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.blog-card:hover {
  box-shadow: var(--shadow-md);
  transform: translateY(-5px);
}

.blog-card-image {
  width: 100%;
  height: 200px;
  overflow: hidden;
  position: relative;
}

.blog-card-image::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 30%;
  background: linear-gradient(to top, rgba(0,0,0,0.5), transparent);
  opacity: 0;
  transition: opacity var(--transition-normal) ease;
}

.blog-card:hover .blog-card-image::after {
  opacity: 1;
}

.blog-card-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform var(--transition-normal) ease;
}

.blog-card:hover .blog-card-image img {
  transform: scale(1.05);
}

.blog-card-content {
  padding: var(--spacing-md);
  flex-grow: 1;
  display: flex;
  flex-direction: column;
}

.blog-card-date {
  display: inline-block;
  font-size: 0.85rem;
  color: var(--medium-gray);
  margin-bottom: var(--spacing-xs);
}

.blog-card-title {
  font-size: 1.4rem;
  margin-bottom: var(--spacing-sm);
  line-height: 1.3;
}

.blog-card-excerpt {
  color: var(--dark-gray);
  margin-bottom: var(--spacing-md);
  flex-grow: 1;
}

.blog-card .btn {
  align-self: flex-start;
}

/* 案例研究部分 */
.case-studies-section {
  background-color: var(--light-gray);
}

.case-study-card {
  display: flex;
  flex-wrap: wrap;
  background-color: var(--white);
  border-radius: var(--radius-md);
  overflow: hidden;
  box-shadow: var(--shadow-sm);
  margin-bottom: var(--spacing-lg);
  transition: all var(--transition-normal) ease;
}

.case-study-card:hover {
  box-shadow: var(--shadow-md);
  transform: translateY(-3px);
}

.case-study-image {
  flex: 1;
  min-width: 300px;
  overflow: hidden;
}

.case-study-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform var(--transition-normal) ease;
}

.case-study-card:hover .case-study-image img {
  transform: scale(1.05);
}

.case-study-content {
  flex: 2;
  min-width: 300px;
  padding: var(--spacing-md);
  display: flex;
  flex-direction: column;
}

.case-study-category {
  display: inline-block;
  padding: 5px 10px;
  background-color: rgba(26, 54, 93, 0.1);
  color: var(--primary-color);
  font-size: 0.85rem;
  border-radius: 20px;
  margin-bottom: var(--spacing-sm);
}

.case-study-title {
  margin-bottom: var(--spacing-sm);
}

/* 联系表单 */
.contact-form {
  background-color: var(--white);
  padding: var(--spacing-lg);
  border-radius: 8px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
}

.form-group {
  margin-bottom: var(--spacing-md);
}

.form-label {
  display: block;
  margin-bottom: var(--spacing-xs);
  color: var(--primary-color);
  font-weight: 500;
}

.form-control {
  width: 100%;
  padding: 12px 15px;
  border: 1px solid #ddd;
  border-radius: var(--radius-sm);
  font-family: var(--body-font);
  font-size: 1rem;
  transition: all var(--transition-normal) ease;
}

.form-control:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(26, 54, 93, 0.1);
}

textarea.form-control {
  min-height: 150px;
  resize: vertical;
}

.form-text {
  font-size: 0.85rem;
  color: var(--medium-gray);
  margin-top: var(--spacing-xs);
}

/* 输入状态样式 */
.form-control.is-valid {
  border-color: #28a745;
}

.form-control.is-invalid {
  border-color: #dc3545;
}

.invalid-feedback {
  display: none;
  color: #dc3545;
  font-size: 0.85rem;
  margin-top: var(--spacing-xs);
}

.was-validated .form-control:invalid + .invalid-feedback {
  display: block;
}

/* 页脚 */
.footer {
  background-color: var(--primary-color);
  color: var(--white);
  padding: var(--spacing-lg) 0;
}

.footer-content {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  gap: var(--spacing-lg);
}

.footer-logo {
  flex: 1;
  min-width: 300px;
}

.footer-links {
  flex: 1;
  min-width: 200px;
}

.footer-links h4 {
  color: var(--white);
  margin-bottom: var(--spacing-md);
}

.footer-links ul {
  list-style: none;
}

.footer-links li {
  margin-bottom: var(--spacing-xs);
}

.footer-links a {
  color: var(--light-gray);
}

.footer-links a:hover {
  color: var(--secondary-color);
}

.social-icons {
  margin-top: var(--spacing-md);
  display: flex;
  gap: var(--spacing-sm);
}

.social-icon {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  transition: background-color 0.3s ease;
}

.social-icon:hover {
  background-color: var(--secondary-color);
}

.social-icon img {
  width: 20px;
  height: 20px;
}

.footer-bottom {
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  margin-top: var(--spacing-lg);
  padding-top: var(--spacing-md);
  text-align: center;
  font-size: 0.9rem;
  color: var(--medium-gray);
}

/* 页面标题区域增强 */
.page-title {
  position: relative;
  padding: var(--spacing-xl) 0;
  color: var(--white);
  text-align: center;
  background-position: center;
  background-size: cover;
  margin-top: 60px;
  overflow: hidden;
}

.page-title .overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(26, 54, 93, 0.9), rgba(26, 54, 93, 0.7));
  z-index: 1;
}

/* 添加图形元素增强视觉冲击力 */
.page-title::before {
  content: '';
  position: absolute;
  top: -50px;
  right: -50px;
  width: 200px;
  height: 200px;
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  z-index: 1;
  animation: pulse 15s infinite alternate;
}

.page-title::after {
  content: '';
  position: absolute;
  bottom: -30px;
  left: -30px;
  width: 150px;
  height: 150px;
  background-color: rgba(255, 255, 255, 0.05);
  border-radius: 50%;
  z-index: 1;
  animation: pulse 10s infinite alternate-reverse;
}

@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 0.05;
  }
  50% {
    transform: scale(1.2);
    opacity: 0.1;
  }
  100% {
    transform: scale(1);
    opacity: 0.05;
  }
}

.page-title .container {
  position: relative;
  z-index: 2;
}

.page-title h1 {
  color: var(--white);
  margin-bottom: var(--spacing-sm);
  position: relative;
  display: inline-block;
  text-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

.page-title h1::after {
  content: '';
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 80px;
  height: 3px;
  background: linear-gradient(to right, var(--secondary-color), var(--accent-color));
  border-radius: 3px;
}

.page-title p {
  font-size: 1.2rem;
  max-width: 800px;
  margin: 0 auto;
  margin-top: var(--spacing-md);
  opacity: 0.9;
}

/* 案例筛选器 */
.case-filter {
  background-color: var(--white);
  padding: var(--spacing-md) 0;
  border-bottom: 1px solid #eee;
}

.filter-wrapper {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  align-items: center;
  gap: var(--spacing-md);
}

.filter-group {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.filter-group label {
  font-weight: 500;
  color: var(--primary-color);
}

.filter-group select {
  padding: 8px 15px;
  border: 1px solid #ddd;
  border-radius: 4px;
  background-color: var(--white);
  font-family: var(--body-font);
  color: var(--dark-gray);
}

.search-box {
  display: flex;
  gap: 5px;
}

.search-box input {
  padding: 8px 15px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-family: var(--body-font);
}

.search-box button {
  padding: 8px 15px;
  background-color: var(--primary-color);
  color: var(--white);
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

/* 案例研究列表 */
.case-studies-list {
  padding: var(--spacing-xl) 0;
}

/* 特色案例 */
.featured-case {
  display: flex;
  flex-wrap: wrap;
  background: var(--white);
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.08);
  margin-bottom: var(--spacing-xl);
}

.featured-case-image {
  flex: 1;
  min-width: 300px;
  position: relative;
}

.featured-case-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.featured-badge {
  position: absolute;
  top: 20px;
  left: 0;
  background-color: var(--secondary-color);
  color: var(--dark-gray);
  padding: 8px 15px;
  font-weight: 600;
  font-size: 0.9rem;
}

.featured-case-content {
  flex: 1;
  min-width: 300px;
  padding: var(--spacing-lg);
}

.case-category {
  display: inline-block;
  font-size: 0.9rem;
  color: var(--primary-color);
  font-weight: 600;
  margin-bottom: var(--spacing-sm);
}

.case-stats {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-md);
  margin: var(--spacing-md) 0;
}

.stat {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.stat-value {
  font-size: 2rem;
  font-weight: 700;
  color: var(--secondary-color);
  line-height: 1;
}

.stat-label {
  font-size: 0.9rem;
  color: var(--dark-gray);
  text-align: center;
}

/* 案例网格 */
.case-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: var(--spacing-lg);
}

.case-card {
  background: var(--white);
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.case-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.case-image {
  height: 200px;
  overflow: hidden;
}

.case-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.5s ease;
}

.case-card:hover .case-image img {
  transform: scale(1.05);
}

.case-content {
  padding: var(--spacing-md);
}

.case-content h3 {
  margin-bottom: var(--spacing-sm);
  font-size: 1.4rem;
}

.case-content p {
  margin-bottom: var(--spacing-md);
  font-size: 1rem;
}

.read-more {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
  color: var(--primary-color);
}

.read-more i {
  transition: transform 0.3s ease;
}

.read-more:hover i {
  transform: translateX(5px);
}

.load-more-container {
  text-align: center;
  margin-top: var(--spacing-xl);
}

/* 客户见证 */
.client-testimonials {
  padding: var(--spacing-xl) 0;
  background-color: var(--light-gray);
}

.client-testimonials h2 {
  text-align: center;
  margin-bottom: var(--spacing-lg);
}

.testimonial-slider {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-lg);
}

.testimonial {
  flex: 1;
  min-width: 300px;
  background: var(--white);
  padding: var(--spacing-lg);
  border-radius: 8px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
}

.testimonial-content {
  margin-bottom: var(--spacing-md);
  font-style: italic;
  color: var(--dark-gray);
}

.testimonial-author {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.testimonial-author img {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  object-fit: cover;
}

.author-info h4 {
  margin-bottom: 0;
  font-family: var(--body-font);
  font-size: 1.1rem;
}

.author-info p {
  margin-bottom: 0;
  font-size: 0.9rem;
  color: var(--medium-gray);
}

/* 行动召唤 */
.cta-section {
  position: relative;
  padding: 80px 0;
  background: linear-gradient(rgba(26, 54, 93, 0.9), rgba(26, 54, 93, 0.9)), url('../images/cta-bg.webp');
  background-size: cover;
  background-position: center;
  color: var(--white);
  text-align: center;
}

.cta-content {
  max-width: 800px;
  margin: 0 auto;
}

.cta-content h2 {
  color: var(--white);
  margin-bottom: var(--spacing-md);
}

.cta-content p {
  font-size: 1.2rem;
  margin-bottom: var(--spacing-lg);
}

.btn-light {
  background-color: var(--white);
  color: var(--primary-color);
}

.btn-light:hover {
  background-color: var(--secondary-color);
  color: var(--dark-gray);
}

.btn-primary {
  background-color: var(--primary-color);
  color: var(--white);
}

.btn-primary:hover {
  background-color: var(--secondary-color);
  color: var(--dark-gray);
}

.btn-outline {
  background-color: transparent;
  border: 2px solid var(--primary-color);
  color: var(--primary-color);
}

.btn-outline:hover {
  background-color: var(--primary-color);
  color: var(--white);
}

.btn-sm {
  padding: 8px 15px;
  font-size: 0.8rem;
}

/* 案例研究详情页样式 */
.case-study-header {
  position: relative;
  padding: var(--spacing-xl) 0;
  color: var(--white);
  background-position: center;
  background-size: cover;
  margin-top: 60px;
}

.case-study-header .overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(26, 54, 93, 0.8);
  z-index: 1;
}

.case-study-header .container {
  position: relative;
  z-index: 2;
}

.breadcrumbs {
  margin-bottom: var(--spacing-md);
  font-size: 0.9rem;
}

.breadcrumbs a {
  color: var(--white);
  text-decoration: underline;
}

.case-study-header h1 {
  color: var(--white);
  margin-bottom: var(--spacing-md);
  max-width: 900px;
}

.case-meta {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-md);
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 10px;
}

.meta-item i {
  font-size: 1.2rem;
}

/* 案例概述 */
.case-summary {
  padding: var(--spacing-xl) 0;
  background-color: var(--white);
}

.summary-container {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-lg);
}

.summary-left {
  flex: 2;
  min-width: 300px;
}

.summary-right {
  flex: 1;
  min-width: 250px;
}

.result-highlight {
  background-color: var(--light-gray);
  padding: var(--spacing-md);
  border-radius: 8px;
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: var(--spacing-md);
}

.result-item {
  text-align: center;
}

/* 案例内容 */
.case-content {
  padding: var(--spacing-xl) 0;
  background-color: var(--light-gray);
}

.case-section {
  background-color: var(--white);
  padding: var(--spacing-lg);
  border-radius: 8px;
  margin-bottom: var(--spacing-lg);
}

.case-section h2 {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: var(--spacing-lg);
}

.case-section h2 i {
  color: var(--secondary-color);
}

/* 挑战卡片 */
.challenges-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: var(--spacing-md);
}

.challenge-card {
  text-align: center;
  padding: var(--spacing-md);
  border: 1px solid #eee;
  border-radius: 8px;
}

.challenge-icon {
  width: 60px;
  height: 60px;
  background-color: var(--light-gray);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto var(--spacing-md);
}

.challenge-icon i {
  font-size: 1.5rem;
  color: var(--primary-color);
}

.challenge-card h3 {
  margin-bottom: var(--spacing-sm);
  font-size: 1.3rem;
}

.challenge-card p {
  margin-bottom: 0;
  font-size: 1rem;
}

/* 解决方案 */
.solution-wrapper {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
}

.solution-item {
  display: flex;
  gap: var(--spacing-md);
}

.solution-number {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--secondary-color);
  opacity: 0.5;
  line-height: 1;
  flex-shrink: 0;
}

.solution-content {
  flex: 1;
}

.solution-content h3 {
  margin-bottom: var(--spacing-sm);
}

.solution-content ul {
  margin-bottom: var(--spacing-md);
  padding-left: var(--spacing-md);
}

.solution-image {
  width: 100%;
  max-width: 600px;
  border-radius: 8px;
  margin-top: var(--spacing-md);
}

/* 流程图 */
.process-diagram {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-sm);
  margin-top: var(--spacing-md);
}

.process-step {
  display: flex;
  align-items: center;
  gap: 10px;
}

.step-name {
  padding: 8px 15px;
  background-color: var(--light-gray);
  border-radius: 4px;
  font-weight: 500;
  font-size: 0.9rem;
}

/* 培训模块 */
.training-modules {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  margin-top: var(--spacing-md);
}

.module {
  padding: 8px 15px;
  background-color: var(--light-gray);
  border-radius: 4px;
  font-size: 0.9rem;
}

/* 时间线 */
.implementation-timeline {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

.timeline-item {
  display: flex;
  gap: var(--spacing-md);
}

.timeline-date {
  flex-shrink: 0;
  width: 80px;
  text-align: center;
  padding: var(--spacing-sm);
  background-color: var(--light-gray);
  border-radius: 4px;
}

.timeline-date .month {
  display: block;
  font-weight: 600;
  color: var(--primary-color);
}

.timeline-date .year {
  display: block;
  font-size: 0.9rem;
  color: var(--medium-gray);
}

.timeline-content {
  flex: 1;
}

.timeline-content h3 {
  margin-bottom: var(--spacing-xs);
  font-size: 1.3rem;
}

.timeline-content p {
  margin-bottom: 0;
}

/* 成果与影响 */
.results-container {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-lg);
}

.results-chart {
  flex: 1;
  min-width: 300px;
}

.results-image {
  width: 100%;
  border-radius: 8px;
}

.results-details {
  flex: 1;
  min-width: 300px;
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

.result-detail-item h3 {
  margin-bottom: var(--spacing-xs);
  font-size: 1.3rem;
}

.result-detail-item p {
  margin-bottom: 0;
}

/* 客户见证 */
.client-testimonial {
  padding: var(--spacing-xl) 0;
  background-color: var(--white);
}

.testimonial-box {
  max-width: 800px;
  margin: 0 auto;
  text-align: center;
  position: relative;
}

.quote-mark {
  font-size: 3rem;
  color: var(--secondary-color);
  opacity: 0.3;
  margin-bottom: var(--spacing-sm);
}

.testimonial-box .testimonial-content {
  font-family: var(--accent-font);
  font-size: 1.4rem;
  margin-bottom: var(--spacing-lg);
}

.testimonial-box .testimonial-author {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-sm);
}

/* 关键经验 */
.key-insights {
  padding: var(--spacing-xl) 0;
  background-color: var(--light-gray);
}

.key-insights h2 {
  text-align: center;
  margin-bottom: var(--spacing-lg);
}

.insights-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: var(--spacing-md);
}

.insight-card {
  background-color: var(--white);
  padding: var(--spacing-md);
  border-radius: 8px;
  text-align: center;
}

.insight-icon {
  width: 60px;
  height: 60px;
  background-color: var(--light-gray);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto var(--spacing-md);
}

.insight-icon i {
  font-size: 1.5rem;
  color: var(--primary-color);
}

.insight-card h3 {
  margin-bottom: var(--spacing-sm);
  font-size: 1.3rem;
}

.insight-card p {
  margin-bottom: 0;
  font-size: 1rem;
}

/* 相关案例 */
.related-cases {
  padding: var(--spacing-xl) 0;
  background-color: var(--white);
}

.related-cases h2 {
  text-align: center;
  margin-bottom: var(--spacing-lg);
}

.related-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: var(--spacing-md);
}

.related-case {
  position: relative;
  border-radius: 8px;
  overflow: hidden;
  height: 200px;
}

.related-case img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.related-case-content {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
  padding: var(--spacing-md);
  color: var(--white);
}

.related-case-content h3 {
  color: var(--white);
  font-size: 1.2rem;
  margin-bottom: var(--spacing-sm);
}

/* 站点页脚 */
.site-footer {
  background-color: var(--primary-color);
  color: var(--white);
  padding: var(--spacing-xl) 0 var(--spacing-md);
}

.footer-content {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-lg);
  margin-bottom: var(--spacing-lg);
}

.footer-about {
  flex: 2;
  min-width: 250px;
}

.footer-links {
  flex: 1;
  min-width: 200px;
}

.footer-contact {
  flex: 1;
  min-width: 200px;
}

.footer-about h3, .footer-links h3, .footer-contact h3 {
  color: var(--white);
  margin-bottom: var(--spacing-md);
  font-size: 1.3rem;
}

.footer-links ul {
  list-style: none;
}

.footer-links li {
  margin-bottom: var(--spacing-xs);
}

.footer-links a {
  color: var(--light-gray);
}

.footer-links a:hover {
  color: var(--secondary-color);
}

.footer-contact p {
  margin-bottom: var(--spacing-sm);
  display: flex;
  align-items: center;
  gap: 10px;
}

.footer-contact i {
  color: var(--secondary-color);
}

.social-icons {
  display: flex;
  gap: 10px;
}

.social-icons a {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  color: var(--white);
  transition: all 0.3s ease;
}

.social-icons a:hover {
  background-color: var(--secondary-color);
  color: var(--dark-gray);
}

.footer-bottom {
  text-align: center;
  padding-top: var(--spacing-md);
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  font-size: 0.9rem;
}

/* 移动导航 */
.mobile-nav {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100vh;
  background-color: rgba(255, 255, 255, 0.98);
  z-index: 1001;
  padding: var(--spacing-lg);
  transform: translateX(-100%);
  transition: all 0.4s cubic-bezier(0.65, 0, 0.35, 1);
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.15);
  overflow-y: auto;
}

.mobile-nav.active {
  transform: translateX(0);
}

.close-mobile-nav {
  position: absolute;
  top: 20px;
  right: 20px;
  font-size: 1.5rem;
  cursor: pointer;
  color: var(--primary-color);
  background: transparent;
  border: none;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.close-mobile-nav:hover {
  background: var(--light-gray);
}

.mobile-nav ul {
  list-style: none;
  margin-top: calc(var(--spacing-xl) + 20px);
  padding: 0;
}

.mobile-nav li {
  margin-bottom: var(--spacing-md);
  opacity: 0;
  transform: translateY(10px);
  transition: opacity 0.3s ease, transform 0.3s ease;
}

.mobile-nav.active li {
  opacity: 1;
  transform: translateY(0);
}

.mobile-nav.active li:nth-child(1) { transition-delay: 0.1s; }
.mobile-nav.active li:nth-child(2) { transition-delay: 0.15s; }
.mobile-nav.active li:nth-child(3) { transition-delay: 0.2s; }
.mobile-nav.active li:nth-child(4) { transition-delay: 0.25s; }
.mobile-nav.active li:nth-child(5) { transition-delay: 0.3s; }

.mobile-nav a {
  font-size: 1.3rem;
  font-weight: 600;
  display: block;
  padding: 12px 0;
  color: var(--primary-color);
  position: relative;
  transition: all 0.3s ease;
}

.mobile-nav a:after {
  content: '';
  position: absolute;
  width: 0;
  height: 2px;
  bottom: 8px;
  left: 0;
  background-color: var(--secondary-color);
  transition: width 0.3s ease;
}

.mobile-nav a:hover {
  color: var(--secondary-color);
  padding-left: 5px;
}

.mobile-nav a:hover:after {
  width: 30px;
}

.mobile-nav a.active {
  color: var(--secondary-color);
}

.mobile-nav a.active:after {
  width: 30px;
}

.mobile-nav-toggle {
  display: none;
  position: fixed;
  right: 15px;
  top: 15px;
  z-index: 9998;
  border: 0;
  background: transparent;
  font-size: 24px;
  transition: all 0.4s;
  outline: none !important;
  line-height: 1;
  cursor: pointer;
  text-align: right;
  width: 44px;
  height: 44px;
  padding: 10px;
  border-radius: 4px;
  color: var(--primary-color);
  background-color: rgba(255, 255, 255, 0.9);
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.mobile-nav-toggle:hover {
  color: var(--secondary-color);
  transform: scale(1.05);
}

.mobile-nav-wrapper {
  display: none;
}

@media (max-width: 991px) {
  .mobile-nav-toggle {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
  }
  
  .mobile-nav-wrapper {
    display: block;
  }
  
  .nav-menu {
    display: none !important;
  }
}

body.mobile-nav-active {
  overflow: hidden !important;
  height: 100%;
  width: 100%;
  position: fixed;
}

/* 增强的排版规范 */
.text-primary {
  color: var(--primary-color);
}

.text-secondary {
  color: var(--secondary-color);
}

.text-accent {
  color: var(--accent-color);
}

.text-tech {
  color: var(--tech-gray);
}

.font-light {
  font-weight: 300;
}

.font-normal {
  font-weight: 400;
}

.font-medium {
  font-weight: 500;
}

.font-bold {
  font-weight: 700;
}

.text-center {
  text-align: center;
}

.text-right {
  text-align: right;
}

.text-left {
  text-align: left;
}

.text-justify {
  text-align: justify;
}

.text-sm {
  font-size: 0.875rem;
}

.text-md {
  font-size: 1rem;
}

.text-lg {
  font-size: 1.125rem;
}

.text-xl {
  font-size: 1.25rem;
}

.text-2xl {
  font-size: 1.5rem;
}

.text-3xl {
  font-size: 1.875rem;
}

.text-4xl {
  font-size: 2.25rem;
}

.leading-tight {
  line-height: 1.25;
}

.leading-normal {
  line-height: 1.5;
}

.leading-loose {
  line-height: 1.75;
}

/* 列表样式增强 */
ul.styled-list, 
ol.styled-list {
  padding-left: 1.5rem;
  margin-bottom: var(--spacing-md);
}

ul.styled-list li,
ol.styled-list li {
  margin-bottom: var(--spacing-xs);
  position: relative;
}

ul.styled-list {
  list-style: none;
}

ul.styled-list li::before {
  content: '';
  position: absolute;
  left: -1.2rem;
  top: 0.6rem;
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background-color: var(--secondary-color);
}

/* 技术列表 - 适合展示技术特点 */
ul.tech-list {
  list-style: none;
  padding-left: 1.8rem;
}

ul.tech-list li {
  position: relative;
  margin-bottom: var(--spacing-sm);
  padding-left: 0.3rem;
}

ul.tech-list li::before {
  content: '→';
  position: absolute;
  left: -1.5rem;
  color: var(--accent-color);
  font-weight: bold;
}

/* 数据列表 - 适合展示统计数据 */
ul.data-list {
  list-style: none;
  padding-left: 0;
}

ul.data-list li {
  position: relative;
  margin-bottom: var(--spacing-sm);
  padding-left: 2rem;
  font-weight: 500;
}

ul.data-list li::before {
  content: '✓';
  position: absolute;
  left: 0;
  color: var(--secondary-color);
  font-weight: bold;
}

/* 段落间距和缩进 */
.text-content p {
  margin-bottom: var(--spacing-md);
  text-align: justify;
  text-justify: inter-word;
}

.text-content h2 {
  margin-top: var(--spacing-lg);
  margin-bottom: var(--spacing-sm);
}

.text-content h3 {
  margin-top: var(--spacing-md);
  margin-bottom: var(--spacing-sm);
}

/* 首字下沉效果 */
.first-letter-drop::first-letter {
  font-size: 3.5em;
  font-weight: 700;
  float: left;
  line-height: 0.8;
  margin-right: 0.1em;
  color: var(--primary-color);
  font-family: var(--title-font);
}

/* 强调文本 */
.highlight {
  background: linear-gradient(transparent 60%, rgba(212, 175, 55, 0.2) 40%);
  padding: 0 2px;
}

.accent-block {
  background-color: rgba(74, 143, 181, 0.1);
  border-left: 3px solid var(--accent-color);
  padding: var(--spacing-sm) var(--spacing-md);
  margin: var(--spacing-md) 0;
  border-radius: 0 var(--radius-sm) var(--radius-sm) 0;
}

.accent-block p:last-child {
  margin-bottom: 0;
}

/* 文本分隔符 */
.text-divider {
  display: flex;
  align-items: center;
  margin: var(--spacing-lg) 0;
}

.text-divider::before,
.text-divider::after {
  content: '';
  flex: 1;
  height: 1px;
  background-color: var(--medium-gray);
  opacity: 0.5;
}

.text-divider::before {
  margin-right: var(--spacing-sm);
}

.text-divider::after {
  margin-left: var(--spacing-sm);
}

/* 图片说明文字 */
.image-caption {
  font-size: 0.9rem;
  color: var(--medium-gray);
  text-align: center;
  margin-top: var(--spacing-xs);
  font-style: italic;
}

/* 图像和图标样式 */
.img-fluid {
  max-width: 100%;
  height: auto;
}

.img-rounded {
  border-radius: var(--radius-md);
}

.img-circle {
  border-radius: 50%;
}

.img-thumbnail {
  padding: 5px;
  background-color: var(--white);
  border: 1px solid #ddd;
  border-radius: var(--radius-sm);
}

.icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: rgba(26, 54, 93, 0.1);
  margin-right: var(--spacing-xs);
}

.icon-sm {
  width: 30px;
  height: 30px;
}

.icon-lg {
  width: 50px;
  height: 50px;
}

/* 徽章样式 */
.badge {
  display: inline-block;
  padding: 3px 8px;
  font-size: 0.75rem;
  font-weight: 500;
  border-radius: 20px;
  background-color: var(--primary-color);
  color: var(--white);
}

.badge-primary {
  background-color: var(--primary-color);
}

.badge-secondary {
  background-color: var(--secondary-color);
  color: var(--dark-gray);
}

.badge-accent {
  background-color: var(--accent-color);
}

.badge-tech {
  background-color: var(--tech-gray);
}

/* 提示框样式 */
.alert {
  padding: var(--spacing-sm) var(--spacing-md);
  border-radius: var(--radius-sm);
  margin-bottom: var(--spacing-md);
  border-left: 4px solid transparent;
}

.alert-primary {
  background-color: rgba(26, 54, 93, 0.1);
  border-left-color: var(--primary-color);
  color: var(--primary-color);
}

.alert-secondary {
  background-color: rgba(212, 175, 55, 0.1);
  border-left-color: var(--secondary-color);
  color: #8a7020;
}

.alert-accent {
  background-color: rgba(74, 143, 181, 0.1);
  border-left-color: var(--accent-color);
  color: var(--accent-color);
}

.alert-warning {
  background-color: rgba(255, 193, 7, 0.1);
  border-left-color: #ffc107;
  color: #856404;
}

.alert-danger {
  background-color: rgba(220, 53, 69, 0.1);
  border-left-color: #dc3545;
  color: #721c24;
} 
@media (max-width: 767px) {
  .footer-content {
      flex-direction: column;
      text-align: center;
  }
  
  .footer-logo, 
  .footer-links {
      margin-bottom: var(--spacing-md);
      text-align: center;
  }

  .footer-links h4 {
      text-align: center;
  }

  .footer-links ul {
      display: flex;
      flex-direction: column;
      align-items: center;
  }

  .footer-links li {
      text-align: center;
  }

  .footer-links a {
      text-align: center;
  }

  .social-icons {
      justify-content: center;
  }

  .footer-bottom {
      text-align: center;
  }
}

/* 全局滚动控制 - 防止双滚动条 */
/* 移除 .swiper-wrapper, overflow: hidden 会干扰布局 */
.swiper-container, [class*="swiper-button-"], [class*="swiper-pagination"] {
  /* overflow: hidden !important;  Swiper JS 会处理容器的 overflow */
}

/* 强制单一滚动条 */
.section, .container, .hero-section, .blog-grid, .case-study-card, .testimonial {
  overflow: visible;
}

/* 性能优化 - 图片加载 */
img {
  max-width: 100%;
  height: auto;
  vertical-align: middle;
  object-fit: cover;
  transform: translateZ(0);
}

/* 性能优化 - 动画帧率 */
@media screen and (max-width: 768px) {
  .animate-on-scroll {
    animation: none !important;
    opacity: 1 !important;
    transform: none !important;
  }
  
  .header.scrolled {
    transform: translateZ(0);
  }
}

/* 性能优化 - 选择器性能 */
.nav-menu > .nav-item > .nav-link {
  position: relative;
  padding: 0.5rem 1rem;
  color: var(--dark-gray);
  text-decoration: none;
  transition: color var(--transition-normal) cubic-bezier(0.4, 0, 0.2, 1);
}