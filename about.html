<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>关于我 - 张洁贞 | 矿业信息化解决方案专家</title>
    <meta name="description" content="了解张洁贞的专业背景、服务理念和成功经验。中矿天智信息科技(徐州)有限公司高级销售经理，专注于智慧矿山信息化系统建设，深耕陕西、山西煤炭市场。">
    
    <!-- DNS预解析 -->
    <link rel="dns-prefetch" href="https://cdnjs.cloudflare.com">
    <link rel="dns-prefetch" href="https://cdn.bootcdn.net">
    
    <!-- 预连接关键第三方域名 -->
    <link rel="preconnect" href="https://cdnjs.cloudflare.com" crossorigin>
    <link rel="preconnect" href="https://cdn.bootcdn.net" crossorigin>
    
    <!-- 预加载关键资源 -->
    <link rel="preload" href="assets/css/main.css" as="style">
    <link rel="preload" href="assets/css/responsive.css" as="style">
    <link rel="preload" href="assets/fonts/noto-sans-sc-regular.woff2" as="font" type="font/woff2" crossorigin>
    <link rel="preload" href="assets/images/about/personal-2.webp" as="image">
    
    <!-- 网站图标 -->
    <link rel="icon" type="image/x-icon" href="assets/images/favicon.ico">
    <link rel="shortcut icon" type="image/x-icon" href="assets/images/favicon.ico">
    <link rel="apple-touch-icon" sizes="180x180" href="assets/images/apple-touch-icon.png">
    
    <!-- 关键CSS -->
    <link rel="stylesheet" href="assets/css/main.css">
    <link rel="stylesheet" href="assets/css/responsive.css">
    
    <!-- 非关键CSS异步加载 -->
    <link rel="stylesheet" href="assets/css/components.css" media="print" onload="this.media='all'">
    <link rel="stylesheet" href="assets/css/page-specific.css" media="print" onload="this.media='all'">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css" media="print" onload="this.media='all'">
    <link rel="stylesheet" href="https://cdn.bootcdn.net/ajax/libs/aos/2.3.4/aos.css" media="print" onload="this.media='all'">
    
    <!-- 性能优化CSS -->
    <link rel="stylesheet" href="assets/css/performance.css">
    
    <!-- 关键脚本 -->
    <script src="https://cdn.bootcdn.net/ajax/libs/nprogress/0.2.0/nprogress.min.js"></script>
    
    <!-- 延迟加载非关键脚本 -->
    <script src="https://cdn.bootcdn.net/ajax/libs/aos/2.3.4/aos.js" defer></script>
    <script src="assets/js/utils.js" defer></script>
    <script src="assets/js/navigation.js" defer></script>
    <script src="assets/js/page-specific.js" defer></script>
    
    <!-- 内联关键CSS -->
    <style>
        /* 首屏关键样式 */
        body { overflow-x: hidden; width: 100%; position: relative; }
        .header { overflow-x: hidden; }
        .container { position: relative; }
        @media (max-width: 991px) {
            .mobile-nav-toggle {
                position: absolute !important;
                right: 15px !important;
                top: 15px !important;
            }
        }
        
        /* 图片加载优化 */
        .lazy-image {
            opacity: 0;
            transition: opacity 0.8s ease-out;
        }
        .lazy-image.loaded {
            opacity: 1;
        }
        
        /* 性能优化类 */
        .optimize-reflow {
            transform: translateZ(0);
            will-change: transform;
        }
        .hardware-accelerated {
            transform: translate3d(0,0,0);
            backface-visibility: hidden;
            perspective: 1000px;
        }
        
        /* 图片加载优化 */
        .image-container {
            position: relative;
            overflow: hidden;
            background: #f5f5f5;
        }
        
        .image-container img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: opacity 0.8s ease-out, transform 1.2s ease-out;
            opacity: 0;
            transform: scale(1.02);
        }
        
        .image-container img.loaded {
            opacity: 1;
            transform: scale(1);
        }
        
        .image-fallback {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: #f5f5f5;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #999;
            font-size: 0.9rem;
        }
    </style>
</head>
<body>
    <!-- 顶部导航栏 -->
    <header class="header">
        <div class="container">
            <nav class="navbar">
                <a href="index.html" class="logo">张<span>洁贞</span><span class="logo-subtitle">矿业信息化专家</span></a>
                <ul class="nav-menu">
                    <li class="nav-item"><a href="index.html" class="nav-link">首页</a></li>
                    <li class="nav-item"><a href="about.html" class="nav-link active">关于我</a></li>
                    <li class="nav-item"><a href="blog/index.html" class="nav-link">洞见</a></li>
                    <li class="nav-item"><a href="case-studies/index.html" class="nav-link">案例</a></li>
                    <li class="nav-item"><a href="contact.html" class="nav-link">联系</a></li>
                    <li class="nav-item"><a href="contact.html" class="nav-contact-btn"><i class="fas fa-headset"></i>咨询我</a></li>
                </ul>
                <button class="mobile-nav-toggle">
                    <i class="fas fa-bars"></i>
                </button>
            </nav>
        </div>
    </header>
    
    <!-- 移动导航菜单 -->
    <div class="mobile-nav-wrapper">
        <div class="mobile-nav">
            <button class="close-mobile-nav">
                <i class="fas fa-times"></i>
            </button>
            <ul>
                <li><a href="index.html">首页</a></li>
                <li><a href="about.html" class="active">关于我</a></li>
                <li><a href="blog/index.html">洞见</a></li>
                <li><a href="case-studies/index.html">案例</a></li>
                <li><a href="contact.html">联系</a></li>
            </ul>
        </div>
        <div class="mobile-nav-backdrop"></div>
    </div>
    
    <!-- 页面标题区 -->
    <section class="about-page-header" data-aos="fade-in" data-aos-duration="1000">
        <div class="about-page-overlay"></div>
        <div class="container" style="position: relative; z-index: 2;">
            <h1 class="about-page-title" data-aos="fade-up" data-aos-delay="200">关于我</h1>
            <p class="about-page-subtitle" data-aos="fade-up" data-aos-delay="300">专注于为煤矿企业提供智能化解决方案，致力于推动矿山数字化转型</p>
        </div>
    </section>
    
    <!-- 个人简介 -->
    <section class="section">
        <div class="container">
            <div class="about-content">
                <div class="about-image optimize-reflow" style="flex: 1.5;" data-aos="fade-right" data-aos-duration="1000">
                    <div class="image-container" style="border-radius: 20px; overflow: hidden; box-shadow: 0 10px 30px rgba(0,0,0,0.1);">
                        <img src="../assets/images/about/personal-2.webp" 
                             alt="张洁贞 - 矿业信息化解决方案专家" 
                             class="gallery-image loading"
                             style="width: 100%; height: 100%; object-fit: cover;"
                             onerror="this.onerror=null; this.parentElement.innerHTML='<div class=\'image-fallback\'>图片加载失败</div>'">
                    </div>
                </div>
                <div class="about-text" style="flex: 2;" data-aos="fade-left" data-aos-duration="1000">
                    <h2>我是张洁贞</h2>
                    <p style="font-size: 1.2rem; color: var(--primary-color); margin-bottom: 1.5rem; font-family: var(--accent-font); font-style: italic;">中矿天智信息科技(徐州)有限公司高级销售经理</p>
                    
                    <p>我目前担任中矿天智信息科技(徐州)有限公司高级销售经理，专注于为煤矿企业提供智能化解决方案，帮助客户实现安全生产和降本增效的数字化转型。</p>
                    
                    <p>凭借对煤矿行业的深入理解和对信息化技术的专业知识，我致力于将先进的物联网、大数据、人工智能等技术与煤矿生产实际需求相结合，为客户量身定制最适合的智能矿山解决方案。我特别擅长煤矿安全监测系统、智能开采平台、矿井通风优化、生产调度管理平台等产品的应用方案设计。</p>
                    
                    <p>在我的职业生涯中，成功服务了陕西、山西等地区的多家大型煤矿集团，通过深入一线、了解客户实际需求，提供精准的解决方案，帮助煤矿企业提升安全管理水平，优化生产效率，实现智能化转型发展。</p>
                    
                    <div style="margin-top: 2rem;">
                        <h4 style="display: inline-block; margin-right: 2rem; margin-bottom: 1rem;">
                            <i class="fas fa-phone" style="color: var(--secondary-color); margin-right: 8px;"></i> 13938155869
                        </h4>
                        <h4 style="display: inline-block; margin-bottom: 1rem;">
                            <i class="fab fa-weixin" style="color: var(--secondary-color); margin-right: 8px;"></i> zhangjiezhen176
                        </h4>
                    </div>
                </div>
            </div>
        </div>
    </section>
    
    <!-- 专业领域 -->
    <section class="section" style="background-color: var(--light-gray);">
        <div class="container">
            <div class="section-title" data-aos="fade-up">
                <h2>我的专业领域</h2>
            </div>
            
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 30px;">
                <!-- 专业领域1 -->
                <div style="background: var(--white); padding: 30px; border-radius: 8px; box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05); transition: transform 0.3s ease;" data-aos="fade-up" data-aos-delay="100">
                    <div style="font-size: 2.5rem; color: var(--secondary-color); margin-bottom: 1rem;">
                        <i class="fas fa-shield-alt icon-pulse"></i>
                    </div>
                    <h3>煤矿安全监测系统</h3>
                    <p>基于物联网和大数据技术的煤矿安全监测预警系统，实时监控瓦斯、一氧化碳等有害气体浓度，井下环境参数和设备运行状态，提前预警潜在安全风险。</p>
                </div>
                
                <!-- 专业领域2 -->
                <div style="background: var(--white); padding: 30px; border-radius: 8px; box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05); transition: transform 0.3s ease;" data-aos="fade-up" data-aos-delay="200">
                    <div style="font-size: 2.5rem; color: var(--secondary-color); margin-bottom: 1rem;">
                        <i class="fas fa-cogs icon-bounce"></i>
                    </div>
                    <h3>智能开采平台</h3>
                    <p>采用智能感知、自动控制技术的综合工作面智能开采系统，实现设备远程监控与自动化作业，提高生产效率和安全性，减少人员井下作业风险。</p>
                </div>
                
                <!-- 专业领域3 -->
                <div style="background: var(--white); padding: 30px; border-radius: 8px; box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05); transition: transform 0.3s ease;" data-aos="fade-up" data-aos-delay="300">
                    <div style="font-size: 2.5rem; color: var(--secondary-color); margin-bottom: 1rem;">
                        <i class="fas fa-wind icon-pulse"></i>
                    </div>
                    <h3>矿井通风优化</h3>
                    <p>基于数字孪生技术的矿井通风网络智能优化系统，实时监测通风状态，优化通风网络结构，提高通风效率，降低能耗，确保井下作业环境安全。</p>
                </div>
                
                <!-- 专业领域4 -->
                <div style="background: var(--white); padding: 30px; border-radius: 8px; box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05); transition: transform 0.3s ease;" data-aos="fade-up" data-aos-delay="100">
                    <div style="font-size: 2.5rem; color: var(--secondary-color); margin-bottom: 1rem;">
                        <i class="fas fa-desktop icon-bounce"></i>
                    </div>
                    <h3>生产调度管理平台</h3>
                    <p>集成生产、运输、通风、安全等多系统数据的煤矿综合管理平台，实现资源统筹调配、生产实时监控、应急指挥调度等功能，助力矿井数字化管理。</p>
                </div>
                
                <!-- 专业领域5（新增） -->
                <div style="background: var(--white); padding: 30px; border-radius: 8px; box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05); transition: transform 0.3s ease;" data-aos="fade-up" data-aos-delay="200">
                    <div style="font-size: 2.5rem; color: var(--secondary-color); margin-bottom: 1rem;">
                        <i class="fas fa-chart-line icon-pulse"></i>
                    </div>
                    <h3>矿山资产管理系统</h3>
                    <p>基于物联网和人工智能技术的矿山设备全生命周期管理平台，实现设备状态实时监控、预测性维护、故障诊断和维修管理，提高设备利用率和安全系数，延长设备使用寿命。</p>
                </div>
                
                <!-- 专业领域6（新增） -->
                <div style="background: var(--white); padding: 30px; border-radius: 8px; box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05); transition: transform 0.3s ease;" data-aos="fade-up" data-aos-delay="300">
                    <div style="font-size: 2.5rem; color: var(--secondary-color); margin-bottom: 1rem;">
                        <i class="fas fa-vr-cardboard icon-bounce"></i>
                    </div>
                    <h3>矿山数字孪生系统</h3>
                    <p>基于三维建模和实时数据的矿山数字孪生平台，构建矿山的虚拟仿真环境，实现生产可视化、设备数字化、管理智能化，为安全生产和科学决策提供全方位支持。</p>
                </div>
            </div>
        </div>
    </section>
    
    <!-- 职业经历 -->
    <section class="section">
        <div class="container">
            <div class="section-title" data-aos="fade-up">
                <h2>职业历程</h2>
            </div>
            
            <!-- 时间线样式 -->
            <div style="position: relative; max-width: 800px; margin: 0 auto;">
                <!-- 中心线 -->
                <div style="position: absolute; width: 4px; background-color: var(--light-gray); top: 0; bottom: 0; left: 50%; margin-left: -2px;"></div>
                
                <!-- 经历1 -->
                <div style="position: relative; width: 45%; margin-bottom: 50px; left: 0;" data-aos="fade-right" data-aos-duration="800">
                    <!-- 顶部金色箭头 -->
                    <div style="position: absolute; width: 0; height: 0; border-left: 10px solid transparent; border-right: 10px solid transparent; border-bottom: 16px solid var(--secondary-color); right: -58px; top: 8px; z-index: 2;"></div>
                    <div style="padding: 20px; background: var(--white); border-radius: 8px; box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);">
                        <h3>中矿天智信息科技(徐州)有限公司</h3>
                        <p style="color: var(--secondary-color); margin-bottom: 15px;">高级销售经理 | 2019年至今</p>
                        <p>负责公司矿山信息化产品在陕西、山西等区域的市场开拓和销售工作，深入了解客户需求，提供定制化解决方案，成功促成多个大型智慧矿山项目落地，为公司创造了显著的业绩增长。</p>
                    </div>
                    <!-- 底部金色原点 -->
                    <div style="position: absolute; width: 20px; height: 20px; background: var(--secondary-color); border-radius: 50%; right: -60px; top: 30px; z-index: 1;"></div>
                </div>
                
                <!-- 经历2 -->
                <div style="position: relative; width: 45%; margin-bottom: 50px; left: 55%;" data-aos="fade-left" data-aos-duration="800" data-aos-delay="200">
                    <!-- 顶部金色箭头 -->
                    <div style="position: absolute; width: 0; height: 0; border-left: 10px solid transparent; border-right: 10px solid transparent; border-bottom: 16px solid var(--secondary-color); left: -58px; top: 8px; z-index: 2;"></div>
                    <div style="padding: 20px; background: var(--white); border-radius: 8px; box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);">
                        <h3>某煤矿机械装备制造企业</h3>
                        <p style="color: var(--secondary-color); margin-bottom: 15px;">区域销售总监 | 2015-2019</p>
                        <p>负责华北地区的市场开发和销售团队管理，主要服务煤炭行业客户，通过深入了解行业需求和痛点，优化销售策略，成功开发了多家大型煤矿集团客户，实现了销售额连续四年增长。</p>
                    </div>
                    <!-- 底部金色原点 -->
                    <div style="position: absolute; width: 20px; height: 20px; background: var(--secondary-color); border-radius: 50%; left: -60px; top: 30px; z-index: 1;"></div>
                </div>
                
                <!-- 经历3 -->
                <div style="position: relative; width: 45%; margin-bottom: 50px; left: 0;" data-aos="fade-right" data-aos-duration="800" data-aos-delay="400">
                    <!-- 顶部金色箭头 -->
                    <div style="position: absolute; width: 0; height: 0; border-left: 10px solid transparent; border-right: 10px solid transparent; border-bottom: 16px solid var(--secondary-color); right: -58px; top: 8px; z-index: 2;"></div>
                    <div style="padding: 20px; background: var(--white); border-radius: 8px; box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);">
                        <h3>某工业自动化设备公司</h3>
                        <p style="color: var(--secondary-color); margin-bottom: 15px;">销售经理 | 2011-2015</p>
                        <p>负责工业自动化控制系统的销售和客户管理，服务对象包括矿山、电力、冶金等行业。通过对工业自动化领域的专业理解，成功为多家企业提供了自动化升级解决方案，建立了良好的行业口碑。</p>
                    </div>
                    <!-- 底部金色原点 -->
                    <div style="position: absolute; width: 20px; height: 20px; background: var(--secondary-color); border-radius: 50%; right: -60px; top: 30px; z-index: 1;"></div>
                </div>
                
                <!-- 经历4 -->
                <div style="position: relative; width: 45%; margin-bottom: 50px; left: 55%;" data-aos="fade-left" data-aos-duration="800" data-aos-delay="600">
                    <!-- 顶部金色箭头 -->
                    <div style="position: absolute; width: 0; height: 0; border-left: 10px solid transparent; border-right: 10px solid transparent; border-bottom: 16px solid var(--secondary-color); left: -58px; top: 8px; z-index: 2;"></div>
                    <div style="padding: 20px; background: var(--white); border-radius: 8px; box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);">
                        <h3>某工控系统集成公司</h3>
                        <p style="color: var(--secondary-color); margin-bottom: 15px;">销售工程师 | 2007-2011</p>
                        <p>从技术支持岗位转为销售工程师，负责工业控制系统解决方案的推广和销售，通过专业知识和技术背景优势，能够准确理解客户需求，提供专业的技术解决方案，获得客户高度认可。</p>
                    </div>
                    <!-- 底部金色原点 -->
                    <div style="position: absolute; width: 20px; height: 20px; background: var(--secondary-color); border-radius: 50%; left: -60px; top: 30px; z-index: 1;"></div>
                </div>
            </div>
        </div>
    </section>
    
    <!-- 核心成就 -->
    <section class="section" style="background-color: var(--primary-color); color: var(--white); position: relative; overflow: hidden;">
        <!-- 背景装饰 -->
        <div style="position: absolute; top: 0; left: 0; right: 0; bottom: 0; background: linear-gradient(135deg, rgba(0,0,0,0.2) 0%, rgba(0,0,0,0) 100%); z-index: 1;"></div>
        <div style="position: absolute; width: 300px; height: 300px; background: radial-gradient(circle, var(--secondary-color) 0%, rgba(255,255,255,0) 70%); opacity: 0.1; top: -150px; right: -150px; border-radius: 50%;"></div>
        <div style="position: absolute; width: 200px; height: 200px; background: radial-gradient(circle, var(--secondary-color) 0%, rgba(255,255,255,0) 70%); opacity: 0.1; bottom: -100px; left: -100px; border-radius: 50%;"></div>
        
        <div class="container" style="position: relative; z-index: 2;">
            <div class="section-title" data-aos="fade-up">
                <h2 style="color: var(--white); font-size: 2.5rem; margin-bottom: 3rem; text-align: center; position: relative;">
                    核心成就
                    <span style="display: block; width: 60px; height: 4px; background: var(--secondary-color); margin: 15px auto 0;"></span>
                </h2>
            </div>
            
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(240px, 1fr)); gap: 30px;">
                <!-- 成就1 -->
                <div style="background: rgba(255,255,255,0.1); backdrop-filter: blur(10px); border-radius: 20px; padding: 35px; text-align: center; transition: transform 0.3s ease, box-shadow 0.3s ease; cursor: pointer; border: 1px solid rgba(255,255,255,0.1);" 
                     data-aos="zoom-in" data-aos-delay="100"
                     onmouseover="this.style.transform='translateY(-10px)'; this.style.boxShadow='0 20px 40px rgba(0,0,0,0.2)';"
                     onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='none';">
                    <div style="font-size: 3.5rem; font-weight: 700; color: var(--secondary-color); margin-bottom: 15px; font-family: var(--accent-font);">30+</div>
                    <h3 style="color: var(--white); margin-bottom: 15px; font-size: 1.5rem;">服务矿企</h3>
                    <p style="color: rgba(255,255,255,0.8); line-height: 1.6;">为陕西、山西等地区30多家煤矿企业提供智能化解决方案</p>
                </div>
                
                <!-- 成就2 -->
                <div style="background: rgba(255,255,255,0.1); backdrop-filter: blur(10px); border-radius: 20px; padding: 35px; text-align: center; transition: transform 0.3s ease, box-shadow 0.3s ease; cursor: pointer; border: 1px solid rgba(255,255,255,0.1);" 
                     data-aos="zoom-in" data-aos-delay="200"
                     onmouseover="this.style.transform='translateY(-10px)'; this.style.boxShadow='0 20px 40px rgba(0,0,0,0.2)';"
                     onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='none';">
                    <div style="font-size: 3.5rem; font-weight: 700; color: var(--secondary-color); margin-bottom: 15px; font-family: var(--accent-font);">45%</div>
                    <h3 style="color: var(--white); margin-bottom: 15px; font-size: 1.5rem;">安全事故降低</h3>
                    <p style="color: rgba(255,255,255,0.8); line-height: 1.6;">客户采用我们的安全监测系统后，安全事故率平均降低45%</p>
                </div>
                
                <!-- 成就3 -->
                <div style="background: rgba(255,255,255,0.1); backdrop-filter: blur(10px); border-radius: 20px; padding: 35px; text-align: center; transition: transform 0.3s ease, box-shadow 0.3s ease; cursor: pointer; border: 1px solid rgba(255,255,255,0.1);" 
                     data-aos="zoom-in" data-aos-delay="300"
                     onmouseover="this.style.transform='translateY(-10px)'; this.style.boxShadow='0 20px 40px rgba(0,0,0,0.2)';"
                     onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='none';">
                    <div style="font-size: 3.5rem; font-weight: 700; color: var(--secondary-color); margin-bottom: 15px; font-family: var(--accent-font);">100+</div>
                    <h3 style="color: var(--white); margin-bottom: 15px; font-size: 1.5rem;">培养销售精英</h3>
                    <p style="color: rgba(255,255,255,0.8); line-height: 1.6;">培养了超过100位优秀销售人才，30%成为管理者</p>
                </div>
                
                <!-- 成就4 -->
                <div style="background: rgba(255,255,255,0.1); backdrop-filter: blur(10px); border-radius: 20px; padding: 35px; text-align: center; transition: transform 0.3s ease, box-shadow 0.3s ease; cursor: pointer; border: 1px solid rgba(255,255,255,0.1);" 
                     data-aos="zoom-in" data-aos-delay="100"
                     onmouseover="this.style.transform='translateY(-10px)'; this.style.boxShadow='0 20px 40px rgba(0,0,0,0.2)';"
                     onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='none';">
                    <div style="font-size: 3.5rem; font-weight: 700; color: var(--secondary-color); margin-bottom: 15px; font-family: var(--accent-font);">12</div>
                    <h3 style="color: var(--white); margin-bottom: 15px; font-size: 1.5rem;">行业领域</h3>
                    <p style="color: rgba(255,255,255,0.8); line-height: 1.6;">跨越IT、制造、医疗、金融等12个行业领域</p>
                </div>
                
                <!-- 成就5 -->
                <div style="background: rgba(255,255,255,0.1); backdrop-filter: blur(10px); border-radius: 20px; padding: 35px; text-align: center; transition: transform 0.3s ease, box-shadow 0.3s ease; cursor: pointer; border: 1px solid rgba(255,255,255,0.1);" 
                     data-aos="zoom-in" data-aos-delay="200"
                     onmouseover="this.style.transform='translateY(-10px)'; this.style.boxShadow='0 20px 40px rgba(0,0,0,0.2)';"
                     onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='none';">
                    <div style="font-size: 3.5rem; font-weight: 700; color: var(--secondary-color); margin-bottom: 15px; font-family: var(--accent-font);">35%</div>
                    <h3 style="color: var(--white); margin-bottom: 15px; font-size: 1.5rem;">生产效率提升</h3>
                    <p style="color: rgba(255,255,255,0.8); line-height: 1.6;">智能开采平台的应用使客户生产效率平均提升35%</p>
                </div>
                
                <!-- 成就6 -->
                <div style="background: rgba(255,255,255,0.1); backdrop-filter: blur(10px); border-radius: 20px; padding: 35px; text-align: center; transition: transform 0.3s ease, box-shadow 0.3s ease; cursor: pointer; border: 1px solid rgba(255,255,255,0.1);" 
                     data-aos="zoom-in" data-aos-delay="300"
                     onmouseover="this.style.transform='translateY(-10px)'; this.style.boxShadow='0 20px 40px rgba(0,0,0,0.2)';"
                     onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='none';">
                    <div style="font-size: 3.5rem; font-weight: 700; color: var(--secondary-color); margin-bottom: 15px; font-family: var(--accent-font);">5+</div>
                    <h3 style="color: var(--white); margin-bottom: 15px; font-size: 1.5rem;">核心专利</h3>
                    <p style="color: rgba(255,255,255,0.8); line-height: 1.6;">参与研发的矿山信息化解决方案获得5项以上核心技术专利</p>
                </div>
                
                <!-- 成就7 -->
                <div style="background: rgba(255,255,255,0.1); backdrop-filter: blur(10px); border-radius: 20px; padding: 35px; text-align: center; transition: transform 0.3s ease, box-shadow 0.3s ease; cursor: pointer; border: 1px solid rgba(255,255,255,0.1);" 
                     data-aos="zoom-in" data-aos-delay="100"
                     onmouseover="this.style.transform='translateY(-10px)'; this.style.boxShadow='0 20px 40px rgba(0,0,0,0.2)';"
                     onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='none';">
                    <div style="font-size: 3.5rem; font-weight: 700; color: var(--secondary-color); margin-bottom: 15px; font-family: var(--accent-font);">20%</div>
                    <h3 style="color: var(--white); margin-bottom: 15px; font-size: 1.5rem;">能源消耗降低</h3>
                    <p style="color: rgba(255,255,255,0.8); line-height: 1.6;">通过智能通风优化系统使客户矿井能源消耗平均降低20%</p>
                </div>
                
                <!-- 成就8 -->
                <div style="background: rgba(255,255,255,0.1); backdrop-filter: blur(10px); border-radius: 20px; padding: 35px; text-align: center; transition: transform 0.3s ease, box-shadow 0.3s ease; cursor: pointer; border: 1px solid rgba(255,255,255,0.1);" 
                     data-aos="zoom-in" data-aos-delay="200"
                     onmouseover="this.style.transform='translateY(-10px)'; this.style.boxShadow='0 20px 40px rgba(0,0,0,0.2)';"
                     onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='none';">
                    <div style="font-size: 3.5rem; font-weight: 700; color: var(--secondary-color); margin-bottom: 15px; font-family: var(--accent-font);">15+</div>
                    <h3 style="color: var(--white); margin-bottom: 15px; font-size: 1.5rem;">行业奖项</h3>
                    <p style="color: rgba(255,255,255,0.8); line-height: 1.6;">所服务项目获得煤炭行业信息化建设优秀案例等15项以上奖项</p>
                </div>
            </div>
        </div>
    </section>
    
    <!-- 教育背景和认证 -->
    <section class="section">
        <div class="container">
            <div class="section-title" data-aos="fade-up">
                <h2>教育背景</h2>
            </div>
            
            <div style="display: flex; flex-wrap: wrap; gap: 40px;">
                <!-- 教育背景 -->
                <div style="flex: 1; min-width: 300px;" data-aos="fade-up" data-aos-delay="100">
                    <h3 style="margin-bottom: 1.5rem;"><i class="fas fa-graduation-cap" style="color: var(--secondary-color); margin-right: 10px;"></i> 学历教育</h3>
                    
                    <div style="margin-bottom: 1.5rem;">
                        <h4>中国矿业大学 工商管理硕士(MBA)</h4>
                        <p style="color: var(--medium-gray);">2000 - 2004</p>
                    </div>
                </div>
            </div>
        </div>
    </section>
    
    <!-- 专业认证 -->
    <section class="section" style="background-color: var(--light-gray);">
        <div class="container">
            <div class="section-title" data-aos="fade-up">
                <h2>专业认证</h2>
            </div>
            
            <div style="display: flex; flex-wrap: wrap; gap: 40px;">
                <div style="flex: 1; min-width: 300px;" data-aos="fade-up" data-aos-delay="200">
                    <h3 style="margin-bottom: 1.5rem;"><i class="fas fa-certificate" style="color: var(--secondary-color); margin-right: 10px;"></i> 行业资质</h3>
                    
                    <div style="margin-bottom: 1.5rem;">
                        <h4>中国煤炭行业安全生产标准化评审员</h4>
                        <p style="color: var(--medium-gray);">2018年获得</p>
                    </div>
                    
                    <div style="margin-bottom: 1.5rem;">
                        <h4>煤矿安全生产管理系统认证专家</h4>
                        <p style="color: var(--medium-gray);">2016年获得</p>
                    </div>
                    
                    <div style="margin-bottom: 1.5rem;">
                        <h4>煤矿信息化系统集成工程师</h4>
                        <p style="color: var(--medium-gray);">2015年获得</p>
                    </div>
                    
                    <div>
                        <h4>智能矿山建设评估师</h4>
                        <p style="color: var(--medium-gray);">2020年获得</p>
                    </div>
                </div>
            </div>
        </div>
    </section>
    
    <!-- 个人生活与价值观 -->
    <section class="section" style="background: linear-gradient(to bottom, var(--light-gray) 0%, #ffffff 100%);">
        <div class="container">
            <div class="section-title" data-aos="fade-up">
                <h2>工作之外的我</h2>
                <p style="color: var(--medium-gray); max-width: 700px; margin: 1rem auto 0; text-align: center;">
                    在专业领域之外，我同样热爱生活的每一个精彩瞬间
                </p>
            </div>
            
            <div class="life-content" style="margin-top: 3rem;">
                <!-- 生活理念展示 -->
                <div class="life-philosophy" style="max-width: 800px; margin: 0 auto 3rem;" data-aos="fade-up">
                    <div style="text-align: center; margin-bottom: 2rem; padding: 2rem; background: linear-gradient(135deg, rgba(255,255,255,0.9) 0%, rgba(255,255,255,0.7) 100%); border-radius: 20px; box-shadow: 0 10px 30px rgba(0,0,0,0.05);">
                        <h3 style="color: var(--primary-color); font-family: var(--accent-font); margin-bottom: 1rem;">生活之道</h3>
                        <p style="font-size: 1.1rem; line-height: 1.8; color: var(--dark-gray);">
                            在矿业信息化领域深耕多年，我始终相信：平衡的生活能带来持续的创新力和工作热情。
                            通过阅读、运动、旅行和艺术，我不断充实自己，以更饱满的状态服务客户。
                        </p>
                    </div>
                </div>

                <!-- 生活画廊 - 创意布局 -->
                <div class="life-gallery optimize-images optimize-reflow" style="display: grid; grid-template-columns: repeat(12, 1fr); gap: 30px; margin: 2rem 0;">
                    <!-- 左侧图片组 -->
                    <div style="grid-column: span 4; display: grid; gap: 25px;">
                        <div class="image-container" style="position: relative; border-radius: 20px; overflow: hidden; box-shadow: 0 10px 30px rgba(0,0,0,0.12); aspect-ratio: 2/3;">
                            <img src="../assets/images/about/life-1.webp" 
                                 alt="专业形象" 
                                 class="gallery-image loading"
                                 onerror="this.onerror=null; this.parentElement.innerHTML='<div class=\'image-fallback\'>图片加载失败</div>'">
                            <div class="overlay" style="position: absolute; bottom: 0; left: 0; right: 0; padding: 30px; background: linear-gradient(to bottom, transparent, rgba(0,0,0,0.7));">
                                <h4 style="color: white; margin: 0; font-size: 1.2rem; font-weight: 500;">专业风采</h4>
                                <p style="color: rgba(255,255,255,0.9); margin: 8px 0 0; font-size: 0.95rem;">矿业信息化解决方案专家</p>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 中间图片组 -->
                    <div style="grid-column: span 4; display: grid; gap: 25px;">
                        <div class="image-container" style="position: relative; border-radius: 20px; overflow: hidden; box-shadow: 0 10px 30px rgba(0,0,0,0.12); aspect-ratio: 2/3;">
                            <img src="../assets/images/about/life-2.webp" 
                                 alt="商务形象" 
                                 class="gallery-image loading"
                                 onerror="this.onerror=null; this.parentElement.innerHTML='<div class=\'image-fallback\'>图片加载失败</div>'">
                            <div class="overlay" style="position: absolute; bottom: 0; left: 0; right: 0; padding: 25px; background: linear-gradient(to bottom, transparent, rgba(0,0,0,0.7));">
                                <h4 style="color: white; margin: 0; font-size: 1.1rem; font-weight: 500;">商务风采</h4>
                                <p style="color: rgba(255,255,255,0.9); margin: 8px 0 0; font-size: 0.95rem;">专业与优雅的完美结合</p>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 右侧图片组 -->
                    <div style="grid-column: span 4; display: grid; gap: 25px;">
                        <div class="image-container" style="position: relative; border-radius: 20px; overflow: hidden; box-shadow: 0 10px 30px rgba(0,0,0,0.12); aspect-ratio: 2/3;">
                            <img src="../assets/images/about/life-3.webp" 
                                 alt="职业形象" 
                                 class="gallery-image loading"
                                 onerror="this.onerror=null; this.parentElement.innerHTML='<div class=\'image-fallback\'>图片加载失败</div>'">
                            <div class="overlay" style="position: absolute; bottom: 0; left: 0; right: 0; padding: 25px; background: linear-gradient(to bottom, transparent, rgba(0,0,0,0.7));">
                                <h4 style="color: white; margin: 0; font-size: 1.1rem; font-weight: 500;">职业风采</h4>
                                <p style="color: rgba(255,255,255,0.9); margin: 8px 0 0; font-size: 0.95rem;">专业与自信的完美诠释</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 生活理念展示区 -->
                <div class="life-moment" style="margin: 3rem 0;" data-aos="fade-up" data-aos-delay="400">
                    <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 30px; align-items: center;">
                        <!-- 左侧文案 -->
                        <div style="padding: 2rem; background: linear-gradient(135deg, rgba(255,255,255,0.9) 0%, rgba(255,255,255,0.7) 100%); border-radius: 20px; box-shadow: 0 10px 30px rgba(0,0,0,0.05);">
                            <h3 style="color: var(--primary-color); font-family: var(--accent-font); margin-bottom: 1.5rem;">生活与工作的平衡</h3>
                            <p style="font-size: 1.1rem; line-height: 1.8; color: var(--dark-gray); margin-bottom: 1.5rem;">
                                在繁忙的工作之余，我注重生活的品质与平衡。通过阅读、运动、旅行等方式，不断充实自己，保持身心健康。
                            </p>
                            <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 15px;">
                                <div style="text-align: center; padding: 1rem; background: rgba(255,255,255,0.8); border-radius: 15px;">
                                    <i class="fas fa-book" style="font-size: 1.5rem; color: var(--secondary-color); margin-bottom: 0.5rem;"></i>
                                    <p style="margin: 0; font-size: 0.9rem;">阅读思考</p>
                                </div>
                                <div style="text-align: center; padding: 1rem; background: rgba(255,255,255,0.8); border-radius: 15px;">
                                    <i class="fas fa-running" style="font-size: 1.5rem; color: var(--secondary-color); margin-bottom: 0.5rem;"></i>
                                    <p style="margin: 0; font-size: 0.9rem;">运动健身</p>
                                </div>
                                <div style="text-align: center; padding: 1rem; background: rgba(255,255,255,0.8); border-radius: 15px;">
                                    <i class="fas fa-plane" style="font-size: 1.5rem; color: var(--secondary-color); margin-bottom: 0.5rem;"></i>
                                    <p style="margin: 0; font-size: 0.9rem;">旅行探索</p>
                                </div>
                                <div style="text-align: center; padding: 1rem; background: rgba(255,255,255,0.8); border-radius: 15px;">
                                    <i class="fas fa-paint-brush" style="font-size: 1.5rem; color: var(--secondary-color); margin-bottom: 0.5rem;"></i>
                                    <p style="margin: 0; font-size: 0.9rem;">艺术欣赏</p>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 右侧图片 -->
                        <div class="image-container" style="position: relative; border-radius: 20px; overflow: hidden; box-shadow: 0 10px 30px rgba(0,0,0,0.12); aspect-ratio: 2/3;">
                            <img src="../assets/images/about/life-8.webp" 
                                 alt="生活剪影" 
                                 class="gallery-image loading"
                                 onerror="this.onerror=null; this.parentElement.innerHTML='<div class=\'image-fallback\'>图片加载失败</div>'">
                            <div class="overlay" style="position: absolute; bottom: 0; left: 0; right: 0; padding: 35px; background: linear-gradient(to bottom, transparent, rgba(0,0,0,0.75));">
                                <h3 style="color: white; margin: 0 0 12px 0; font-family: var(--accent-font); font-size: 1.5rem; font-weight: 500;">工作之余的生活点滴</h3>
                                <p style="color: rgba(255,255,255,0.95); margin: 0; font-size: 1.1rem; line-height: 1.6;">在专业追求与个人成长间找到完美平衡</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 更新CSS动画效果 -->
        <style>
            .gallery-item {
                transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
                cursor: pointer;
                position: relative;
                background: var(--white);
            }
            .gallery-item::after {
                content: '';
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background: linear-gradient(to bottom, transparent 50%, rgba(0,0,0,0.1));
                opacity: 0;
                transition: opacity 0.5s ease;
            }
            .gallery-item:hover {
                transform: translateY(-10px);
                box-shadow: 0 15px 40px rgba(0,0,0,0.2);
            }
            .gallery-item:hover::after {
                opacity: 1;
            }
            .gallery-item img {
                transition: transform 0.5s cubic-bezier(0.4, 0, 0.2, 1);
            }
            .gallery-item:hover img {
                transform: scale(1.05);
            }
            .overlay {
                transition: all 0.4s ease;
                background: linear-gradient(to bottom, transparent, rgba(0,0,0,0.75));
                opacity: 0.95;
            }
            .gallery-item:hover .overlay {
                opacity: 1;
                padding-bottom: 40px;
            }
            @media (max-width: 992px) {
                .life-gallery {
                    grid-template-columns: repeat(2, 1fr) !important;
                    gap: 20px !important;
                }
                .life-gallery > div {
                    grid-column: span 1 !important;
                }
                .life-moment > div {
                    grid-template-columns: 1fr !important;
                    gap: 20px !important;
                }
            }
            @media (max-width: 576px) {
                .life-gallery {
                    grid-template-columns: 1fr !important;
                }
                .gallery-item {
                    aspect-ratio: 2/3 !important;
                }
            }
        </style>
    </section>
    
    <!-- 联系方式 -->
    <section class="section">
        <div class="container">
            <div class="section-title" data-aos="fade-up">
                <h2>联系方式</h2>
            </div>
            
            <div style="display: flex; flex-wrap: wrap; gap: 40px;">
                <!-- 联系信息 -->
                <div style="flex: 1; min-width: 300px;" data-aos="fade-right" data-aos-delay="100">
                    <h3 style="margin-bottom: 1.5rem;"><i class="fas fa-address-card" style="color: var(--secondary-color); margin-right: 10px;"></i> 直接联系</h3>
                    
                    <div style="margin-bottom: 1rem;">
                        <h4><i class="fas fa-envelope" style="color: var(--secondary-color); margin-right: 10px;"></i> 电子邮件</h4>
                        <p><EMAIL></p>
                    </div>
                    
                    <div style="margin-bottom: 1rem;">
                        <h4><i class="fas fa-phone" style="color: var(--secondary-color); margin-right: 10px;"></i> 电话</h4>
                        <p>13938195869</p>
                    </div>
                    
                    <div>
                        <h4><i class="fas fa-map-marker-alt" style="color: var(--secondary-color); margin-right: 10px;"></i> 地址</h4>
                        <p>陕西省神木市店塔镇</p>
                    </div>
                </div>
                
                <!-- 社交媒体 -->
                <div style="flex: 1; min-width: 300px;" data-aos="fade-left" data-aos-delay="200">
                    <h3 style="margin-bottom: 1.5rem;"><i class="fas fa-share-alt" style="color: var(--secondary-color); margin-right: 10px;"></i> 社交媒体</h3>
                    
                    <div style="display: flex; gap: 15px; margin-bottom: 20px;">
                        <a href="#" style="text-decoration: none; color: var(--primary-color); font-size: 1.5rem;"><i class="fab fa-weixin"></i></a>
                        <a href="#" style="text-decoration: none; color: var(--primary-color); font-size: 1.5rem;"><i class="fab fa-weibo"></i></a>
                        <a href="#" style="text-decoration: none; color: var(--primary-color); font-size: 1.5rem;"><i class="fab fa-linkedin"></i></a>
                    </div>
                    
                    <div>
                        <h4>工作时间</h4>
                        <p>周一至周日: 9:00 - 18:00</p>
                        <p>全年无休服务</p>
                    </div>
                </div>
            </div>
        </div>
    </section>
    
    <!-- 联系CTA -->
    <section class="section" style="background: linear-gradient(rgba(26, 54, 93, 0.9), rgba(26, 54, 93, 0.9)), url('assets/images/background-texture.webp'); background-size: cover; color: var(--white); text-align: center;">
        <div class="container">
            <h2 class="no-decoration" style="color: var(--white);">期待与您合作</h2>
            <p style="font-size: 1.2rem; max-width: 700px; margin: 0 auto 2rem auto;">如果您希望了解更多我的专业服务，或探讨如何帮助您的企业提升销售业绩，请随时联系我。</p>
            <div style="display: flex; justify-content: center; gap: 30px;">
                <a href="contact.html" class="btn" style="background-color: var(--secondary-color); color: var(--dark-gray); min-width: 150px;">联系我</a>
                <a href="case-studies/index.html" class="about-case-btn" style="min-width: 150px;">查看成功案例</a>
            </div>
        </div>
    </section>
    
    <!-- 页脚部分 -->
    <footer class="footer" data-aos="fade-up" data-aos-duration="800">
        <div class="container">
            <div class="footer-content">
                <div class="footer-logo">
                    <h3>张<span>洁贞</span></h3>
                    <p>中矿天智信息科技(徐州)有限公司高级销售经理，专注矿山智能化解决方案。</p>
                    <div class="social-icons">
                        <a href="#" class="social-icon" title="抖音">
                            <img src="assets/images/svg/douyin.svg" alt="抖音" class="social-media-icon">
                        </a>
                        <a href="#" class="social-icon" title="小红书">
                            <img src="assets/images/svg/xiaohongshu.svg" alt="小红书" class="social-media-icon">
                        </a>
                        <a href="#" class="social-icon" title="微信">
                            <img src="assets/images/svg/weixin.svg" alt="微信" class="social-media-icon">
                        </a>
                        <a href="#" class="social-icon" title="播客">
                            <img src="assets/images/svg/boke.svg" alt="播客" class="social-media-icon">
                        </a>
                    </div>
                </div>
                
                <div class="footer-links">
                    <h4>网站导航</h4>
                    <ul>
                        <li><a href="index.html">首页</a></li>
                        <li><a href="about.html">关于我</a></li>
                        <li><a href="blog/index.html">专业洞见</a></li>
                        <li><a href="case-studies/index.html">成功案例</a></li>
                        <li><a href="contact.html">联系方式</a></li>
                    </ul>
                </div>
                
                <div class="footer-links">
                    <h4>联系方式</h4>
                    <ul>
                        <li>
                            <img src="assets/images/svg/email.svg" alt="邮箱" class="footer-contact-icon">
                            <a href="mailto:13938155869">13938155869</a>
                        </li>
                        <li>
                            <img src="assets/images/svg/dianhua.svg" alt="电话" class="footer-contact-icon">
                            <a href="tel:+8613938155869">+86 139 3815 5869</a>
                        </li>
                        <li>
                            <img src="assets/images/svg/weixin.svg" alt="微信" class="footer-contact-icon">
                            <span class="wechat-id">zhangjiezhen176</span>
                        </li>
                    </ul>
                </div>
            </div>
            
            <div class="footer-bottom">
                <p>&copy; 2025 张洁贞 - 矿业信息化解决方案专家.</p>
            </div>
        </div>
    </footer>
    
    <!-- JavaScript 文件 -->
    <script>
        // 图片加载处理
        document.addEventListener('DOMContentLoaded', function() {
            const images = document.querySelectorAll('.gallery-image');
            
            function loadImage(img) {
                img.classList.remove('loading');
                img.classList.add('loaded');
            }
            
            function handleImageError(img) {
                const container = img.parentElement;
                container.innerHTML = '<div class="image-fallback">图片加载失败</div>';
            }
            
            images.forEach(img => {
                if (img.complete) {
                    loadImage(img);
                } else {
                    img.addEventListener('load', () => loadImage(img));
                    img.addEventListener('error', () => handleImageError(img));
                }
                
                // 尝试不同的路径
                const originalSrc = img.src;
                img.onerror = function() {
                    // 尝试不同的路径组合
                    const paths = [
                        originalSrc,
                        originalSrc.replace('../assets', 'assets'),
                        originalSrc.replace('assets', '../assets'),
                        originalSrc.replace('../assets/images', 'assets/images'),
                        originalSrc.replace('assets/images', '../assets/images')
                    ];
                    
                    let currentPathIndex = 1;
                    const tryNextPath = () => {
                        if (currentPathIndex < paths.length) {
                            img.src = paths[currentPathIndex];
                            currentPathIndex++;
                        } else {
                            handleImageError(img);
                        }
                    };
                    
                    tryNextPath();
                };
            });
        });

        // 初始化AOS动画库
        document.addEventListener('DOMContentLoaded', function() {
            AOS.init({
                duration: 800,
                easing: 'ease-in-out',
                once: true,
                disable: window.innerWidth < 768
            });
        });
        
        // 页面加载进度条
        NProgress.configure({ showSpinner: false });
        NProgress.start();
        window.addEventListener('load', function() {
            NProgress.done();
        });
    </script>
</body>
</html> 