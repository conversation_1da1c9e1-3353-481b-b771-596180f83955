<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, user-scalable=no, viewport-fit=cover">
    <title>张洁贞 - 矿业信息化解决方案专家 | 中矿天智信息科技--中国矿业大学</title>
    <meta name="description" content="张洁贞，中矿天智信息科技(徐州)有限公司高级销售经理，专注于智慧矿山数字化解决方案，深耕陕西、山西等煤矿集团市场，助力煤矿安全高效智能化转型。">
    <meta name="baidu-site-verification" content="验证码" />
    
    <!-- 中国主流浏览器支持 -->
    <meta name="screen-orientation" content="portrait">
    <meta name="x5-orientation" content="portrait">
    <meta name="x5-fullscreen" content="true">
    <meta name="x5-page-mode" content="app">
    
    <!-- 关键CSS文件预加载和优先加载 -->
    <link rel="preload" href="assets/css/main.css" as="style">
    <link rel="preload" href="assets/css/responsive.css" as="style">
    <link rel="stylesheet" href="assets/css/main.css">
    <link rel="stylesheet" href="assets/css/responsive.css">
    
    <!-- 预加载关键字体文件 -->
    
    <!-- 延迟加载非关键CSS -->
    <link rel="stylesheet" href="assets/css/components.css" media="print" onload="this.media='all'">
    <link rel="stylesheet" href="assets/css/page-specific.css" media="print" onload="this.media='all'">
    
    <!-- 服务、成就和客户部分的样式 -->
    <style>
        /* 服务卡片网格布局 */
        .services-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
            gap: 30px;
            margin-top: 40px;
        }
        
        /* 服务卡片样式 */
        .service-card {
            background: #ffffff;
            border-radius: 12px;
            padding: 30px;
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.05);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            border: 1px solid rgba(0, 0, 0, 0.08);
            display: flex;
            flex-direction: column;
            height: 100%;
        }
        
        .service-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
        }
        
        .service-icon {
            font-size: 40px;
            color: #3172b9;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            width: 80px;
            height: 80px;
            background: rgba(49, 114, 185, 0.1);
            border-radius: 50%;
        }
        
        .service-title {
            font-size: 20px;
            font-weight: 600;
            margin-bottom: 15px;
            color: #333;
        }
        
        .service-description {
            color: #666;
            line-height: 1.6;
            margin-bottom: 20px;
            flex-grow: 1;
        }
        
        /* 专业成就部分 */
        .achievements-section {
            margin-top: 60px;
            background: linear-gradient(120deg, #f8f9fa, #e9ecef);
            padding: 40px;
            border-radius: 12px;
        }
        
        .achievements-section .section-title {
            margin-bottom: 30px;
        }
        
        .achievements-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 20px;
            text-align: center;
        }
        
        .achievement-card {
            background: #ffffff;
            padding: 25px 15px;
            border-radius: 8px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
            transition: transform 0.3s ease;
        }
        
        .achievement-card:hover {
            transform: translateY(-5px);
        }
        
        .achievement-number {
            display: block;
            font-size: 36px;
            font-weight: 700;
            color: #3172b9;
            margin-bottom: 5px;
        }
        
        .achievement-text {
            color: #555;
            font-size: 16px;
        }
        
        /* 合作客户部分 */
        .clients-section {
            margin-top: 60px;
            padding: 40px 0;
        }
        
        .clients-section .section-title {
            margin-bottom: 30px;
        }
        
        .clients-grid {
            display: flex;
            flex-wrap: wrap;
            justify-content: center;
            align-items: center;
            gap: 40px;
        }
        
        .client-logo {
            display: flex;
            flex-direction: column;
            align-items: center;
            transition: transform 0.3s ease;
            text-align: center;
        }
        
        .client-logo:hover {
            transform: scale(1.1);
        }
        
        .client-logo img {
            filter: grayscale(0%);
            opacity: 1;
            transition: filter 0.3s ease, opacity 0.3s ease;
            max-width: 120px;
            height: auto;
        }
        
        .client-logo:hover img {
            filter: grayscale(0%);
            opacity: 1;
        }
        
        .client-name {
            margin-top: 10px;
            font-size: 14px;
            color: #666;
        }
        
        /* 响应式调整 */
        @media (max-width: 768px) {
            .services-grid {
                grid-template-columns: 1fr;
            }
            
            .achievements-grid {
                grid-template-columns: repeat(2, 1fr);
            }
            
            .client-logo {
                flex-basis: 40%;
            }
            
            .achievements-section,
            .clients-section {
                padding: 30px 15px;
            }
        }
        
        @media (max-width: 480px) {
            .achievements-grid {
                grid-template-columns: 1fr;
            }
            
            .client-logo {
                flex-basis: 100%;
            }
            
            .service-card,
            .achievement-card {
                padding: 20px 15px;
            }
        }
    </style>
    
    <!-- 网站图标 - SVG格式更清晰且体积更小 -->
    <link rel="icon" type="image/svg+xml" href="assets/images/svg/logo.svg">
    <link rel="alternate icon" href="assets/images/favicon.png" type="image/png">
    <link rel="apple-touch-icon" href="assets/images/apple-touch-icon.png">
    
    <!-- Font Awesome 异步加载 - 使用国内CDN -->
    <link rel="preconnect" href="https://cdn.bootcdn.net">
    <link rel="stylesheet" href="https://cdn.bootcdn.net/ajax/libs/font-awesome/5.15.4/css/all.min.css" media="print" onload="this.media='all'">
    <noscript><link rel="stylesheet" href="https://cdn.bootcdn.net/ajax/libs/font-awesome/5.15.4/css/all.min.css"></noscript>
    
    <!-- AOS动画库 - 延迟加载 -->
    <link rel="stylesheet" href="https://cdn.bootcdn.net/ajax/libs/aos/2.3.4/aos.css" media="print" onload="this.media='all'">
    <script src="https://cdn.bootcdn.net/ajax/libs/aos/2.3.4/aos.js" defer></script>
    
    <!-- NProgress加载进度条 - 立即加载 -->
    <link rel="preload" href="https://cdn.bootcdn.net/ajax/libs/nprogress/0.2.0/nprogress.min.css" as="style">
    <link rel="stylesheet" href="https://cdn.bootcdn.net/ajax/libs/nprogress/0.2.0/nprogress.min.css">
    <script src="https://cdn.bootcdn.net/ajax/libs/nprogress/0.2.0/nprogress.min.js"></script>
    
    <!-- Swiper轮播库 - 延迟加载 -->
    <link rel="stylesheet" href="https://cdn.bootcdn.net/ajax/libs/Swiper/8.4.5/swiper-bundle.min.css" media="print" onload="this.media='all'">
    <script src="https://cdn.bootcdn.net/ajax/libs/Swiper/8.4.5/swiper-bundle.min.js" defer></script>
    
    <!-- 百度统计 -->
    <script>
    var _hmt = _hmt || [];
    (function() {
      var hm = document.createElement("script");
      hm.src = "https://hm.baidu.com/hm.js?YOUR_SITE_ID";
      var s = document.getElementsByTagName("script")[0]; 
      s.parentNode.insertBefore(hm, s);
    })();
    </script>
    
    <!-- 结构化数据标记 - 面包屑 -->
    <script type="application/ld+json">
    {
      "@context": "https://ziyuan.baidu.com/contexts/cambrian.jsonld",
      "@id": "https://www.zhangjiezhen.com",
      "appid": "百度熊掌ID",
      "title": "矿业信息化解决方案专家 - 张洁贞",
      "description": "专注于智慧矿山、安全生产和数字化转型的专业顾问",
      "pubDate": "2023-05-01T08:00:00"
    }
    </script>
    
    <!-- 预加载关键图片资源 -->
    <link rel="preload" as="image" href="assets/images/hero/mining-operation.webp">
    
    <!-- 预加载footer关键资源 -->
    <link rel="preload" href="assets/css/main.css" as="style">
    <link rel="preload" href="assets/images/svg/email.svg" as="image" type="image/svg+xml">
    <link rel="preload" href="assets/images/svg/dianhua.svg" as="image" type="image/svg+xml">
    <link rel="preload" href="assets/images/svg/weixin.svg" as="image" type="image/svg+xml">
    
    <!-- 添加性能优化CSS -->
    <link rel="stylesheet" href="assets/css/performance.css">
</head>
<body>
    <!-- 顶部导航栏 -->
    <header class="header">
        <div class="container">
            <nav class="navbar">
                <a href="index.html" class="logo">张<span>洁贞</span><span class="logo-subtitle">矿业信息化专家</span></a>
                <ul class="nav-menu">
                    <li class="nav-item"><a href="index.html" class="nav-link active">首页</a></li>
                    <li class="nav-item"><a href="about.html" class="nav-link">关于我</a></li>
                    <li class="nav-item"><a href="blog/index.html" class="nav-link">洞见</a></li>
                    <li class="nav-item"><a href="case-studies/index.html" class="nav-link">案例</a></li>
                    <li class="nav-item"><a href="contact.html" class="nav-link">联系</a></li>
                    <li class="nav-item"><a href="contact.html" class="nav-contact-btn"><i class="fas fa-headset"></i>咨询我</a></li>
                </ul>
                <button class="mobile-nav-toggle">
                    <i class="fas fa-bars"></i>
                </button>
            </nav>
        </div>
    </header>
    
    <!-- 移动导航菜单 -->
    <div class="mobile-nav-wrapper">
        <div class="mobile-nav">
            <button class="close-mobile-nav">
                <i class="fas fa-times"></i>
            </button>
            <ul>
                <li><a href="index.html" class="active">首页</a></li>
                <li><a href="about.html">关于我</a></li>
                <li><a href="blog/index.html">洞见</a></li>
                <li><a href="case-studies/index.html">案例</a></li>
                <li><a href="contact.html">联系</a></li>
            </ul>
        </div>
        <div class="mobile-nav-backdrop"></div>
    </div>
    
    <!-- Hero Section -->
    <section class="home-hero-section">
        <!-- 背景图片和渐变叠加 -->
        <div class="hero-overlay" data-aos="fade-in" data-aos-duration="1000" data-skeleton="image">
            <img src="assets/images/hero/mining-operation-low.webp" data-src="assets/images/hero/mining-operation.webp" alt="智慧矿山背景" class="hero-image progressive-image" width="1920" height="1080" fetchpriority="high" data-low-quality-src="assets/images/hero/mining-operation-low.webp">
            <div class="hero-gradient"></div>
        </div>
        
        <!-- 内容区域 -->
        <div class="container hero-content-container">
            <div class="hero-content" data-skeleton="text">
                <h1 class="hero-title" data-aos="fade-up" data-aos-delay="200">智慧矿山，安全高效</h1>
                <p class="hero-subtitle" data-aos="fade-up" data-aos-delay="300">创新矿业信息化解决方案，助力煤矿数字化转型</p>
                <div class="hero-buttons" data-aos="fade-up" data-aos-delay="400">
                    <a href="about.html" class="hero-button-primary">了解更多</a>
                    <a href="contact.html" class="hero-button-secondary">联系我</a>
                </div>
            </div>
        </div>
    </section>
    
    <!-- 关于我简介 -->
    <section class="section about-section">
        <div class="container">
            <div class="section-title" data-aos="fade-up">
                <h2>关于我</h2>
            </div>
            <div class="about-content">
                <div class="about-image" data-aos="fade-right" data-aos-duration="1000">
                    <!-- 此处应放置个人形象照片 -->
                    <img src="assets/images/about/personal-1.webp" alt="张洁贞 - 矿业信息化解决方案专家-中国矿业大学" title="张洁贞" style="max-width: 100%; width: auto; height: auto; object-fit: contain;" loading="lazy">
                </div>
                <div class="about-text" data-aos="fade-left" data-aos-duration="1000">
                    <h3>矿业信息化解决方案专家-中国矿业大学</h3>
                    <p>作为中矿天智信息科技(徐州)有限公司的高级销售经理，我专注于为煤矿企业提供先进的智能化解决方案，帮助客户实现安全生产、降本增效的数字化转型。</p>
                    <p>我的专业领域涵盖矿山安全监测系统、智能开采平台、矿井通风优化、生产调度管理平台等多种矿业信息化产品，尤其擅长结合客户具体需求，提供定制化的综合解决方案。</p>
                    <p>在我的职业生涯中，成功服务了陕西、山西等地区的30多家大型煤矿集团，协助客户实现了安全事故率平均下降45%，生产效率提升35%，为煤矿企业的智能化转型贡献了专业力量。</p>
                    <a href="about.html" class="btn">查看完整简介</a>
                </div>
            </div>
        </div>
    </section>
    
    <!-- 我的服务 -->
    <section class="section services-section">
        <div class="container">
            <div class="section-title" data-aos="fade-up">
                <h2>我的服务</h2>
            </div>
            
            <div class="services-grid">
                <!-- 服务卡片1 -->
                <div class="service-card" data-aos="fade-up" data-aos-delay="100">
                    <div class="service-icon">
                        <i class="fas fa-shield-alt"></i>
                    </div>
                    <h3 class="service-title">矿山安全监测系统</h3>
                    <p class="service-description">利用物联网技术实时监测瓦斯、粉尘、水文等安全指标，建立全面的安全预警机制，大幅降低事故发生率。</p>
                    <a href="services/safety-monitoring.html" class="btn btn-secondary">了解更多</a>
                </div>
                
                <!-- 服务卡片2 -->
                <div class="service-card" data-aos="fade-up" data-aos-delay="200">
                    <div class="service-icon">
                        <i class="fas fa-cogs"></i>
                    </div>
                    <h3 class="service-title">智能开采解决方案</h3>
                    <p class="service-description">基于工作面智能化开采技术，实现采煤工作面无人值守，提高产量的同时保障安全生产。</p>
                    <a href="services/intelligent-mining.html" class="btn btn-secondary">了解更多</a>
                </div>
                
                <!-- 服务卡片3 -->
                <div class="service-card" data-aos="fade-up" data-aos-delay="300">
                    <div class="service-icon">
                        <i class="fas fa-wind"></i>
                    </div>
                    <h3 class="service-title">矿井通风优化系统</h3>
                    <p class="service-description">通过数值模拟和智能控制技术，优化矿井通风系统，降低能耗的同时提升通风效果，创造安全作业环境。</p>
                    <a href="services/ventilation-optimization.html" class="btn btn-secondary">了解更多</a>
                </div>
                
                <!-- 服务卡片4 -->
                <div class="service-card" data-aos="fade-up" data-aos-delay="400">
                    <div class="service-icon">
                        <i class="fas fa-desktop"></i>
                    </div>
                    <h3 class="service-title">矿山生产调度平台</h3>
                    <p class="service-description">整合人员、设备、物料等资源，打造一体化生产调度管理平台，提升生产效率，降低运营成本。</p>
                    <a href="services/production-management.html" class="btn btn-secondary">了解更多</a>
                </div>
                
                <!-- 服务卡片5 (新增) -->
                <div class="service-card" data-aos="fade-up" data-aos-delay="500">
                    <div class="service-icon">
                        <i class="fas fa-robot"></i>
                    </div>
                    <h3 class="service-title">智能巡检机器人系统</h3>
                    <p class="service-description">采用AI视觉识别和5G通信技术，实现井下设备智能巡检，提高巡检效率，降低人工风险。</p>
                    <a href="services/robot-inspection.html" class="btn btn-secondary">了解更多</a>
                </div>
                
                <!-- 服务卡片6 (新增) -->
                <div class="service-card" data-aos="fade-up" data-aos-delay="600">
                    <div class="service-icon">
                        <i class="fas fa-project-diagram"></i>
                    </div>
                    <h3 class="service-title">数字孪生可视化平台</h3>
                    <p class="service-description">构建矿山数字孪生模型，实现生产全流程可视化监控，支持智能决策和预测性维护。</p>
                    <a href="services/digital-twin.html" class="btn btn-secondary">了解更多</a>
                </div>
            </div>
            
            <!-- 专业成就 -->
            <div class="achievements-section" data-aos="fade-up" data-aos-delay="500">
                <div class="section-title" data-aos="fade-up">
                    <h2>专业成就</h2>
                </div>
                <div class="achievements-grid">
                    <div class="achievement-card">
                        <span class="achievement-number">30+</span>
                        <span class="achievement-text">服务煤矿集团</span>
                    </div>
                    <div class="achievement-card">
                        <span class="achievement-number">50+</span>
                        <span class="achievement-text">成功项目案例</span>
                    </div>
                    <div class="achievement-card">
                        <span class="achievement-number">45%</span>
                        <span class="achievement-text">安全事故率下降</span>
                    </div>
                    <div class="achievement-card">
                        <span class="achievement-number">35%</span>
                        <span class="achievement-text">生产效率提升</span>
                    </div>
                    <!-- 新增成就数据 -->
                    <div class="achievement-card">
                        <span class="achievement-number">25%</span>
                        <span class="achievement-text">能源成本降低</span>
                    </div>
                    <div class="achievement-card">
                        <span class="achievement-number">85%</span>
                        <span class="achievement-text">设备故障预警率</span>
                    </div>
                    <div class="achievement-card">
                        <span class="achievement-number">95%</span>
                        <span class="achievement-text">客户满意度</span>
                    </div>
                    <div class="achievement-card">
                        <span class="achievement-number">15+</span>
                        <span class="achievement-text">技术专利授权</span>
                    </div>
                </div>
            </div>
            
            <!-- 合作客户 -->
            <div class="clients-section" data-aos="fade-up" data-aos-delay="600">
                <div class="section-title" data-aos="fade-up">
                    <h2>合作客户</h2>
                </div>
                <div class="clients-grid">
                    <div class="client-logo">
                        <img class="progressive-image" src="assets/images/placeholder.svg" data-src="assets/images/clients/shaanxi-coal.webp" alt="陕西煤业" width="120" height="80">
                        <span class="client-name">陕西煤业</span>
                    </div>
                    <div class="client-logo">
                        <img src="assets/images/clients/shanxi-coking.webp" alt="山西焦煤" width="120" height="80" loading="lazy">
                        <span class="client-name">山西焦煤</span>
                    </div>
                    <div class="client-logo">
                        <img src="assets/images/clients/pingmei-group.webp" alt="平煤集团" width="120" height="80" loading="lazy">
                        <span class="client-name">平煤集团</span>
                    </div>
                    <div class="client-logo">
                        <img src="assets/images/clients/shenhua-group.webp" alt="神华集团" width="120" height="80" loading="lazy">
                        <span class="client-name">神华集团</span>
                    </div>
                    <div class="client-logo">
                        <img src="assets/images/clients/china-coal.webp" alt="中煤能源" width="120" height="80" loading="lazy">
                        <span class="client-name">中煤能源</span>
                    </div>
                </div>
            </div>
        </div>
    </section>
    
    <!-- 最新博客/洞见 -->
    <section class="section">
        <div class="container">
            <div class="section-title" data-aos="fade-up">
                <h2>专业洞见</h2>
            </div>
            <div class="blog-grid">
                <!-- 博客文章1 -->
                <div class="blog-card" data-aos="fade-up" data-aos-delay="100">
                    <div class="blog-card-image">
                        <img class="progressive-image" src="assets/images/placeholder.svg" data-src="assets/images/blog/article-thumbnail-1.webp" alt="矿山安全监测预警系统的构建与应用" width="400" height="250">
                    </div>
                    <div class="blog-card-content">
                        <span class="blog-card-category">安全监测</span>
                        <div class="blog-card-meta">
                            <span class="blog-card-date">2023年12月15日</span>
                            <span class="blog-card-read-time"><i class="fas fa-clock"></i> 8分钟阅读</span>
                        </div>
                        <h3 class="blog-card-title">矿山安全监测预警系统的构建与应用</h3>
                        <p class="blog-card-excerpt">现代煤矿企业安全生产的首要保障，源于科学完善的监测预警体系。本文分析了基于物联网技术的安全监测系统如何有效预防事故发生...</p>
                        <a href="blog/safety-monitoring.html" class="btn btn-secondary">阅读全文</a>
                    </div>
                </div>
                
                <!-- 博客文章2 -->
                <div class="blog-card" data-aos="fade-up" data-aos-delay="200">
                    <div class="blog-card-image">
                        <img src="assets/images/blog/article-thumbnail-2.webp" alt="煤矿智能开采关键技术与实践案例" width="400" height="250" loading="lazy">
                    </div>
                    <div class="blog-card-content">
                        <span class="blog-card-category">智能开采</span>
                        <div class="blog-card-meta">
                            <span class="blog-card-date">2023年11月28日</span>
                            <span class="blog-card-read-time"><i class="fas fa-clock"></i> 10分钟阅读</span>
                        </div>
                        <h3 class="blog-card-title">煤矿智能开采关键技术与实践案例</h3>
                        <p class="blog-card-excerpt">智能开采是煤矿企业降本增效的核心手段。本文详细介绍了工作面智能化开采系统的关键技术及其在陕西某煤矿的成功应用案例...</p>
                        <a href="blog/intelligent-mining.html" class="btn btn-secondary">阅读全文</a>
                    </div>
                </div>
                
                <!-- 博客文章3 -->
                <div class="blog-card" data-aos="fade-up" data-aos-delay="300">
                    <div class="blog-card-image">
                        <img src="assets/images/blog/article-thumbnail-3.webp" alt="数字孪生技术在现代煤矿中的应用前景" width="400" height="250" loading="lazy">
                    </div>
                    <div class="blog-card-content">
                        <span class="blog-card-category">数字孪生</span>
                        <div class="blog-card-meta">
                            <span class="blog-card-date">2023年10月10日</span>
                            <span class="blog-card-read-time"><i class="fas fa-clock"></i> 7分钟阅读</span>
                        </div>
                        <h3 class="blog-card-title">数字孪生技术在现代煤矿中的应用前景</h3>
                        <p class="blog-card-excerpt">数字孪生技术为煤矿提供了虚实结合的智能管理新模式，实现了从被动应对到主动预测的转变。本文探讨了该技术在煤矿领域的创新应用...</p>
                        <a href="blog/digital-twin.html" class="btn btn-secondary">阅读全文</a>
                    </div>
                </div>
                
                <!-- 博客文章4 (新增) -->
                <div class="blog-card" data-aos="fade-up" data-aos-delay="400">
                    <div class="blog-card-image">
                        <img src="assets/images/blog/article-thumbnail-4.webp" alt="5G+AI技术在煤矿智能巡检中的创新应用" width="400" height="250" loading="lazy">
                    </div>
                    <div class="blog-card-content">
                        <span class="blog-card-category">技术前沿</span>
                        <div class="blog-card-meta">
                            <span class="blog-card-date">2023年7月20日</span>
                            <span class="blog-card-read-time"><i class="fas fa-clock"></i> 9分钟阅读</span>
                        </div>
                        <h3 class="blog-card-title">5G+AI技术在煤矿智能巡检中的创新应用</h3>
                        <p class="blog-card-excerpt">5G与人工智能技术的结合为煤矿智能巡检带来了革命性变化。本文分享了利用5G网络和AI识别技术打造无人智能巡检系统的成功案例...</p>
                        <a href="blog/5g-ai-inspection.html" class="btn btn-secondary">阅读全文</a>
                    </div>
                </div>
                
                <!-- 博客文章5 (新增) -->
                <div class="blog-card" data-aos="fade-up" data-aos-delay="500">
                    <div class="blog-card-image">
                        <img src="assets/images/blog/article-thumbnail-5.webp" alt="矿井通风系统优化技术与能耗管理" width="400" height="250" loading="lazy">
                    </div>
                    <div class="blog-card-content">
                        <span class="blog-card-category">通风优化</span>
                        <div class="blog-card-meta">
                            <span class="blog-card-date">2023年6月15日</span>
                            <span class="blog-card-read-time"><i class="fas fa-clock"></i> 6分钟阅读</span>
                        </div>
                        <h3 class="blog-card-title">矿井通风系统优化技术与能耗管理</h3>
                        <p class="blog-card-excerpt">通风系统是煤矿安全生产的命脉，也是能耗的主要部分。本文介绍了基于CFD技术的矿井通风网络智能调控方案，如何在保障安全的前提下实现能源节约...</p>
                        <a href="blog/ventilation-optimization.html" class="btn btn-secondary">阅读全文</a>
                    </div>
                </div>
                
                <!-- 博客文章6 (新增) -->
                <div class="blog-card" data-aos="fade-up" data-aos-delay="600">
                    <div class="blog-card-image">
                        <img src="assets/images/blog/article-thumbnail-6.webp" alt="智能矿山安全管理系统架构与实践" width="400" height="250" loading="lazy">
                    </div>
                    <div class="blog-card-content">
                        <span class="blog-card-category">安全系统</span>
                        <div class="blog-card-meta">
                            <span class="blog-card-date">2023年5月18日</span>
                            <span class="blog-card-read-time"><i class="fas fa-clock"></i> 11分钟阅读</span>
                        </div>
                        <h3 class="blog-card-title">智能矿山安全管理系统架构与实践</h3>
                        <p class="blog-card-excerpt">智能化是煤矿安全管理的发展方向。本文详细阐述了智能矿山安全管理系统的多层架构设计，以及基于大数据分析的安全风险评估模型在实际项目中的应用成果...</p>
                        <a href="blog/smart-mining-safety.html" class="btn btn-secondary">阅读全文</a>
                    </div>
                </div>
            </div>
            <div class="text-center" style="text-align: center; margin-top: 2rem;" data-aos="fade-up" data-aos-delay="400">
                <a href="blog/index.html" class="btn">浏览更多洞见</a>
            </div>
        </div>
    </section>
    
    <!-- 案例研究亮点 -->
    <section class="section case-studies-section">
        <div class="container">
            <div class="section-title" data-aos="fade-up">
                <h2>成功案例</h2>
            </div>
            
            <!-- 案例1 -->
            <div class="case-study-card" data-aos="fade-up" data-aos-duration="800">
                <div class="case-study-image">
                    <img class="progressive-image" src="assets/images/placeholder.svg" data-src="assets/images/case-studies/case-thumbnail-1.webp" alt="陕西省某大型煤矿集团智能化系统升级" width="500" height="300">
                </div>
                <div class="case-study-content">
                    <span class="case-study-category">智能矿山建设</span>
                    <h3 class="case-study-title">陕西省某大型煤矿集团智能化系统升级</h3>
                    <p>为陕西省领先煤矿集团提供了全面的智能化系统升级方案，包括安全监测系统改造、生产调度中心建设、井下通信网络优化等，实现了从传统生产向智能矿山的转型...</p>
                    
                    <div class="case-study-stats">
                        <div class="case-study-stat">
                            <div class="case-study-stat-icon">
                                <i class="fas fa-shield-alt"></i>
                            </div>
                            <div class="case-study-stat-text">安全事故率下降 <span>58%</span></div>
                        </div>
                        <div class="case-study-stat">
                            <div class="case-study-stat-icon">
                                <i class="fas fa-chart-line"></i>
                            </div>
                            <div class="case-study-stat-text">生产效率提升 <span>42%</span></div>
                        </div>
                        <div class="case-study-stat">
                            <div class="case-study-stat-icon">
                                <i class="fas fa-wallet"></i>
                            </div>
                            <div class="case-study-stat-text">运营成本降低 <span>25%</span></div>
                        </div>
                    </div>
                    
                    <a href="case-studies/case-tech-team.html" class="btn btn-secondary">查看详情</a>
                </div>
            </div>
            
            <!-- 案例2 -->
            <div class="case-study-card" data-aos="fade-up" data-aos-duration="800" data-aos-delay="200">
                <div class="case-study-image">
                    <img src="assets/images/case-studies/case-thumbnail-2.webp" alt="山西省煤炭企业安全生产监管平台建设" width="500" height="300" loading="lazy">
                </div>
                <div class="case-study-content">
                    <span class="case-study-category">安全监测系统</span>
                    <h3 class="case-study-title">山西省煤炭企业安全生产监管平台建设</h3>
                    <p>为山西省多家煤矿企业构建了统一的安全生产监管平台，集成了瓦斯监测、人员定位、视频监控等多个子系统，建立了完善的安全预警机制，显著提升了安全管理水平...</p>
                    
                    <div class="case-study-stats">
                        <div class="case-study-stat">
                            <div class="case-study-stat-icon">
                                <i class="fas fa-network-wired"></i>
                            </div>
                            <div class="case-study-stat-text">联网矿井 <span>15家</span></div>
                        </div>
                        <div class="case-study-stat">
                            <div class="case-study-stat-icon">
                                <i class="fas fa-stopwatch"></i>
                            </div>
                            <div class="case-study-stat-text">应急响应时间缩短 <span>65%</span></div>
                        </div>
                        <div class="case-study-stat">
                            <div class="case-study-stat-icon">
                                <i class="fas fa-check-circle"></i>
                            </div>
                            <div class="case-study-stat-text">安全监管覆盖率 <span>100%</span></div>
                        </div>
                    </div>
                    
                    <a href="case-studies/case-bank-rebranding.html" class="btn btn-secondary">查看详情</a>
                </div>
            </div>
            
            <!-- 案例3 (新增) -->
            <div class="case-study-card" data-aos="fade-up" data-aos-duration="800" data-aos-delay="300">
                <div class="case-study-image">
                    <img src="assets/images/case-studies/case-thumbnail-3.webp" alt="内蒙古煤矿5G+AI智能巡检系统部署" width="500" height="300" loading="lazy">
                </div>
                <div class="case-study-content">
                    <span class="case-study-category">智能巡检系统</span>
                    <h3 class="case-study-title">内蒙古煤矿5G+AI智能巡检系统部署</h3>
                    <p>为内蒙古大型煤矿部署了基于5G网络的AI智能巡检系统，实现了井下环境的实时监测与异常自动识别，显著减少了人工巡检的安全风险，提升了巡检的全面性和准确性...</p>
                    
                    <div class="case-study-stats">
                        <div class="case-study-stat">
                            <div class="case-study-stat-icon">
                                <i class="fas fa-robot"></i>
                            </div>
                            <div class="case-study-stat-text">巡检覆盖率提升至 <span>95%</span></div>
                        </div>
                        <div class="case-study-stat">
                            <div class="case-study-stat-icon">
                                <i class="fas fa-user-shield"></i>
                            </div>
                            <div class="case-study-stat-text">人员安全风险降低 <span>70%</span></div>
                        </div>
                        <div class="case-study-stat">
                            <div class="case-study-stat-icon">
                                <i class="fas fa-tachometer-alt"></i>
                            </div>
                            <div class="case-study-stat-text">故障预警时间缩短 <span>85%</span></div>
                        </div>
                    </div>
                    
                    <a href="case-studies/case-5g-ai-inspection.html" class="btn btn-secondary">查看详情</a>
                </div>
            </div>
            
            <div class="text-center" style="text-align: center; margin-top: 2rem;" data-aos="fade-up" data-aos-delay="300">
                <a href="case-studies/index.html" class="btn">查看更多案例</a>
            </div>
        </div>
    </section>
    
    <!-- 客户评价 -->
    <section class="section" data-aos="fade-up" data-aos-duration="800">
        <div class="container">
            <div class="section-title">
                <h2>客户评价</h2>
            </div>
            
            <div class="testimonial-swiper" style="max-width: 900px; margin: 0 auto; padding: 0 20px; box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05); border-radius: 12px; background: linear-gradient(160deg, #ffffff, #f9f9f9); overflow: hidden;">
                <div class="swiper-wrapper">
                    <!-- 评价1 -->
                    <div class="swiper-slide">
                        <div class="testimonial">
                            <div class="testimonial-content">
                                <i class="fas fa-quote-left testimonial-quote-icon"></i>
                                <p class="testimonial-text">张洁贞经理深入了解我们矿区的实际需求，为我们量身定制了智能化升级方案。她专业的服务态度和对煤矿安全生产的深刻理解，帮助我们顺利完成了数字化转型，安全生产水平得到显著提升。</p>
                            </div>
                            <div class="testimonial-author">
                                <img src="assets/images/testimonials/client1.webp" alt="王志远" class="testimonial-author-image optimize-image" width="60" height="60" loading="lazy">
                                <div class="author-info">
                                    <h4>王志远</h4>
                                    <p>陕西某煤矿集团安全总监</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 评价2 -->
                    <div class="swiper-slide">
                        <div class="testimonial">
                            <div class="testimonial-content">
                                <i class="fas fa-quote-left testimonial-quote-icon"></i>
                                <p class="testimonial-text">与张洁贞合作是一次关键的决策。她不仅精通各类矿山信息化系统，更能结合我们矿区的具体条件提供实用的解决方案。中矿天智的技术和她的专业服务，帮助我们实现了安全高效生产的目标。</p>
                            </div>
                            <div class="testimonial-author">
                                <img src="assets/images/testimonials/client2.webp" alt="李梦华" class="testimonial-author-image optimize-image" width="60" height="60" loading="lazy">
                                <div class="author-info">
                                    <h4>李总</h4>
                                    <p>山西某煤业公司总工程师</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 评价3 -->
                    <div class="swiper-slide">
                        <div class="testimonial">
                            <div class="testimonial-content">
                                <i class="fas fa-quote-left testimonial-quote-icon"></i>
                                <p class="testimonial-text">数字孪生系统的应用让我们对整个矿区的运营状况了如指掌。决策效率提升明显，现在我们可以更快速准确地应对各种生产情况。</p>
                            </div>
                            <div class="testimonial-author">
                                <img src="assets/images/testimonials/client3.webp" alt="张明辉" class="testimonial-author-image optimize-image" width="60" height="60" loading="lazy">
                                <div class="author-info">
                                    <h4>张经理</h4>
                                    <p>山东某矿业集团生产经理</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 评价4 -->
                    <div class="swiper-slide">
                        <div class="testimonial">
                            <div class="testimonial-content">
                                <i class="fas fa-quote-left testimonial-quote-icon"></i>
                                <p class="testimonial-text">与张洁贞合作开发智能调度平台是我们企业数字化转型的重要一步。她对业务流程的深刻理解和信息化系统的专业知识，帮助我们建立了高效、可视化的生产调度体系，大幅提升了协同效率。</p>
                            </div>
                            <div class="testimonial-author">
                                <img src="assets/images/testimonials/client4.webp" alt="赵主任" class="testimonial-author-image optimize-image" width="60" height="60" loading="lazy">
                                <div class="author-info">
                                    <h4>赵主任</h4>
                                    <p>内蒙古某煤矿信息化主管</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- 添加分页器 -->
                <div class="swiper-pagination"></div>
                <!-- 添加导航按钮 -->
                <div class="swiper-button-prev"></div>
                <div class="swiper-button-next"></div>
            </div>
        </div>
    </section>
    
    <!-- 联系我 CTA -->
    <section class="section cta-section-home" data-aos="fade-in" data-aos-duration="400" data-aos-once="true">
        <div class="container">
            <h2 class="cta-title-home">准备好开启矿山智能化转型了吗？</h2>
            <p class="cta-text-home">无论您是需要安全监测系统升级、生产管理优化，还是全面的智慧矿山解决方案，我们都能为您量身定制。</p>
            <a href="contact.html" class="btn cta-button-home">立即联系</a>
        </div>
    </section>

    <style>
        /* CTA 部分性能优化 */
        .cta-section-home {
            will-change: opacity;
            transform: translateZ(0);
            opacity: 0;
            transition: opacity 0.3s ease-out;
        }

        .cta-section-home .container {
            transform: translateZ(0);
            opacity: 0;
            transform: translateY(10px);
            transition: all 0.3s ease-out;
        }

        .cta-section-home.aos-animate {
            opacity: 1;
        }

        .cta-section-home.aos-animate .container {
            opacity: 1;
            transform: translateY(0);
        }

        .cta-button-home {
            position: relative;
            transition: transform 0.2s ease-out;
        }

        .cta-button-home:hover {
            transform: translateY(-2px);
        }

        /* 性能优化：减少不必要的3D变换 */
        @media (max-width: 768px) {
            .cta-section-home,
            .cta-section-home .container {
                transform: none;
                will-change: auto;
            }
        }

        /* 降级方案 */
        .cta-section-home.performance-degraded,
        .cta-section-home.performance-degraded .container {
            transition: none;
            transform: none;
            opacity: 1;
        }

        @media (prefers-reduced-motion: reduce) {
            .cta-section-home,
            .cta-section-home .container,
            .cta-button-home {
                transition: none !important;
                transform: none !important;
                animation: none !important;
                opacity: 1 !important;
            }
        }
    </style>

    <script>
        // CTA 部分性能优化
        document.addEventListener('DOMContentLoaded', function() {
            const ctaSection = document.querySelector('.cta-section-home');
            if (!ctaSection) return;

            let performanceDegraded = false;
            let animationStartTime = 0;

            // 使用 requestIdleCallback 延迟初始化性能监控
            const initPerformanceMonitoring = () => {
                if ('PerformanceObserver' in window) {
                    try {
                        const observer = new PerformanceObserver((list) => {
                            if (performanceDegraded) return; // 已经降级就不再处理

                            const entries = list.getEntries();
                            const longTasks = entries.filter(entry => 
                                entry.entryType === 'longtask' && 
                                entry.duration > 100 // 提高阈值到100ms
                            );

                            if (longTasks.length > 2) { // 只有多次出现长任务才降级
                                performanceDegraded = true;
                                degradePerformance();
                            }
                        });
                        
                        observer.observe({ entryTypes: ['longtask'] });
                    } catch (e) {
                        // 静默失败，不影响主要功能
                    }
                }
            };

            // 性能降级函数
            const degradePerformance = () => {
                if (!ctaSection) return;
                ctaSection.classList.add('performance-degraded');
            };

            // 使用 Intersection Observer 优化动画触发
            const ctaObserver = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting && !performanceDegraded) {
                        animationStartTime = performance.now();
                        
                        requestAnimationFrame(() => {
                            entry.target.style.opacity = '1';
                            entry.target.querySelector('.container').style.opacity = '1';
                            entry.target.querySelector('.container').style.transform = 'translateY(0)';
                        });

                        // 简化的性能监控
                        setTimeout(() => {
                            const duration = performance.now() - animationStartTime;
                            if (duration > 500) { // 提高阈值，减少误报
                                degradePerformance();
                            }
                        }, 400);

                        ctaObserver.unobserve(entry.target);
                    }
                });
            }, {
                threshold: 0.1, // 降低阈值，提前开始动画
                rootMargin: '50px'
            });

            ctaObserver.observe(ctaSection);

            // 使用 requestIdleCallback 延迟初始化性能监控
            if ('requestIdleCallback' in window) {
                requestIdleCallback(initPerformanceMonitoring);
            } else {
                setTimeout(initPerformanceMonitoring, 1000);
            }
        });
    </script>
    
    <!-- 页脚部分 - 性能优化 -->
    <footer class="footer optimize-reflow critical-render">
        <div class="container viewport-content">
            <div class="footer-content hardware-accelerated">
                <div class="footer-logo">
                    <h3>张<span>洁贞</span></h3>
                    <p>中矿天智信息科技(徐州)有限公司高级销售经理，专注矿山智能化解决方案。</p>
                    <div class="social-icons optimize-images">
                        <a href="#" class="social-icon lazy-load" title="抖音">
                            <img src="assets/images/svg/douyin.svg" alt="抖音" class="social-media-icon" loading="lazy">
                        </a>
                        <a href="#" class="social-icon lazy-load" title="小红书">
                            <img src="assets/images/svg/xiaohongshu.svg" alt="小红书" class="social-media-icon" loading="lazy">
                        </a>
                        <a href="#" class="social-icon lazy-load" title="微信">
                            <img src="assets/images/svg/weixin.svg" alt="微信" class="social-media-icon" loading="lazy">
                        </a>
                        <a href="#" class="social-icon lazy-load" title="播客">
                            <img src="assets/images/svg/boke.svg" alt="播客" class="social-media-icon" loading="lazy">
                        </a>
                    </div>
                </div>
                
                <div class="footer-links">
                    <h4>网站导航</h4>
                    <ul>
                        <li><a href="index.html">首页</a></li>
                        <li><a href="about.html">关于我</a></li>
                        <li><a href="blog/index.html">专业洞见</a></li>
                        <li><a href="case-studies/index.html">成功案例</a></li>
                        <li><a href="contact.html">联系方式</a></li>
                    </ul>
                </div>
                
                <div class="footer-links">
                    <h4>联系方式</h4>
                    <ul>
                        <li>
                            <img src="assets/images/svg/email.svg" alt="邮箱" class="footer-contact-icon" loading="eager">
                            <a href="mailto:13938155869">13938155869</a>
                        </li>
                        <li>
                            <img src="assets/images/svg/dianhua.svg" alt="电话" class="footer-contact-icon" loading="eager">
                            <a href="tel:+8613938155869">+86 139 3815 5869</a>
                        </li>
                        <li>
                            <img src="assets/images/svg/weixin.svg" alt="微信" class="footer-contact-icon" loading="eager">
                            <span class="wechat-id">zhangjiezhen176</span>
                        </li>
                    </ul>
                </div>
            </div>
            
            <div class="footer-bottom">
                <p>&copy; 2025 张洁贞 - 矿业信息化解决方案专家.</p>
            </div>
        </div>
    </footer>
    
    <!-- JavaScript 文件 - 优化加载 -->
    <script>
        // 页面加载进度条
        NProgress.configure({ showSpinner: false });
        NProgress.start();
        window.addEventListener('load', function() {
            NProgress.done();
        });
    </script>
    
    <!-- 延迟加载非关键JavaScript -->
    <script src="assets/js/utils.js" defer></script>
    <script src="assets/js/navigation.js" defer></script>
    <script src="assets/js/page-specific.js" defer></script>
    
    <script>
        // 初始化AOS动画库 - 优化触发时机
        document.addEventListener('DOMContentLoaded', function() {
            // 使用requestIdleCallback优化初始化时机
            if ('requestIdleCallback' in window) {
                requestIdleCallback(function() {
                    AOS.init({
                        duration: 800,
                        easing: 'ease-in-out',
                        once: true,
                        disable: 'mobile' // 移动设备上禁用动画提高性能
                    });
                });
            } else {
                // 降级处理
                setTimeout(function() {
                    AOS.init({
                        duration: 800,
                        easing: 'ease-in-out',
                        once: true,
                        disable: 'mobile'
                    });
                }, 100);
            }
        });
        
        // 初始化客户评价 Swiper
        // 功能: 初始化客户评价轮播图
        // 输入: Swiper 库已加载, HTML 结构已存在
        // 处理: 创建 Swiper 实例, 配置分页器、导航按钮、自动播放等选项
        // 输出: 启动的轮播图效果
        // 定义: testimonialSwiperInstance - Swiper 实例
        document.addEventListener('DOMContentLoaded', function() {
            // 确保 Swiper 库已加载 (虽然 defer 应该能保证，但加个检查更保险)
            if (typeof Swiper === 'function') {
                const testimonialSwiperInstance = new Swiper('.testimonial-swiper', {
                    // Optional parameters
                    loop: true, // 循环播放
                    autoplay: {
                      delay: 5000, // 自动播放间隔 5 秒
                      disableOnInteraction: false, // 用户交互后不停止自动播放
                    },
                    pagination: {
                      el: '.swiper-pagination', // 分页器元素
                      clickable: true, // 分页器可点击切换
                    },
                    navigation: {
                      nextEl: '.swiper-button-next', // 下一页按钮
                      prevEl: '.swiper-button-prev', // 上一页按钮
                    },
                    spaceBetween: 30, // Slide 之间的间距
                    effect: 'slide', // 切换效果
                    grabCursor: true, // 鼠标悬停时显示抓取手势
                });
            } else {
                console.error('Swiper library not loaded yet.');
            }
        });

        // 响应式处理客户评价组件
        (function() {
            function adjustTestimonialStyles() {
                const testimonialSwiper = document.querySelector('.testimonial-swiper');
                if (!testimonialSwiper) return;
                
                if (window.innerWidth <= 768) {
                    // 移动端样式
                    testimonialSwiper.style.boxShadow = 'none';
                    testimonialSwiper.style.borderRadius = '0';
                    testimonialSwiper.style.padding = '0';
                    testimonialSwiper.style.background = 'transparent';
                } else {
                    // PC端样式恢复
                    testimonialSwiper.style.boxShadow = '0 10px 30px rgba(0, 0, 0, 0.05)';
                    testimonialSwiper.style.borderRadius = '12px';
                    testimonialSwiper.style.padding = '0 20px';
                    testimonialSwiper.style.background = 'linear-gradient(160deg, #ffffff, #f9f9f9)';
                }
            }
            
            // 添加防抖动函数
            function debounce(func, wait) {
                let timeout;
                return function() {
                    const context = this, args = arguments;
                    clearTimeout(timeout);
                    timeout = setTimeout(function() {
                        func.apply(context, args);
                    }, wait);
                };
            }
            
            // 优化窗口resize事件
            const debouncedAdjust = debounce(adjustTestimonialStyles, 150);
            
            // 页面加载后延迟执行
            if ('requestIdleCallback' in window) {
                requestIdleCallback(adjustTestimonialStyles);
            } else {
                setTimeout(adjustTestimonialStyles, 100);
            }
            
            // 窗口大小改变时执行
            window.addEventListener('resize', debouncedAdjust);
        })();
    </script>
    
    <!-- 添加Intersection Observer优化 -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 优化footer图片加载
            const footerImages = document.querySelectorAll('.footer img[loading="lazy"]');
            const imageObserver = new IntersectionObserver((entries, observer) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const img = entry.target;
                        img.classList.add('loaded');
                        observer.unobserve(img);
                    }
                });
            }, {
                rootMargin: '50px 0px',
                threshold: 0.1
            });

            footerImages.forEach(img => imageObserver.observe(img));

            // 优化footer内容加载
            const footerContent = document.querySelector('.footer-content');
            const contentObserver = new IntersectionObserver((entries, observer) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.style.opacity = '1';
                        observer.unobserve(entry.target);
                    }
                });
            }, {
                threshold: 0.1
            });

            if (footerContent) {
                contentObserver.observe(footerContent);
            }
        });
    </script>
    
    <!-- 骨架屏与预加载 -->
    <div class="preloader" id="sitePreloader">
        <div class="preloader-spinner"></div>
    </div>
    
    <!-- 性能优化组件 -->
    <script src="assets/js/components/loading-manager.js"></script>
    <script src="assets/js/components/progressive-image.js"></script>
    <script src="assets/js/components/cache-manager.js"></script>
    <script src="assets/js/components/wechat-share.js"></script>
    <script src="assets/js/components/network-detector.js"></script>
    
    <!-- 初始化脚本 -->
    <script>
    document.addEventListener('DOMContentLoaded', function() {
        // 检测网络速度
        if ('connection' in navigator && navigator.connection.effectiveType === 'slow-2g') {
            document.body.classList.add('slow-connection');
        }
        
        // 在页面加载完成后隐藏预加载动画
        document.addEventListener('page:fully-loaded', function() {
            const preloader = document.getElementById('sitePreloader');
            if (preloader) {
                preloader.classList.add('hidden');
                setTimeout(() => {
                    preloader.remove();
                }, 500);
            }
        });
    });
    </script>
</body>
</html>