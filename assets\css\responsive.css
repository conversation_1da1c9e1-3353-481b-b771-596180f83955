/* 
 * 资深女销售专家个人品牌网站
 * 响应式样式表
 */

/* 大屏幕设备 (大于 1200px) */
@media (min-width: 1201px) {
  .container {
    max-width: 1140px;
  }
}

/* 桌面设备 (992px 到 1200px) */
@media (max-width: 1200px) {
  .container {
    max-width: 960px;
  }
  
  .hero-title {
    font-size: 3.5rem;
  }
  
  .hero-subtitle {
    font-size: 1.3rem;
  }
}

/* 大屏幕设备（桌面电脑等） */
@media (min-width: 992px) {
  .mobile-nav-toggle {
    display: none !important;
    opacity: 0 !important;
    visibility: hidden !important;
    pointer-events: none !important;
    position: absolute !important;
    z-index: -1 !important;
  }
  
  .nav-menu {
    display: flex;
  }
  
  /* 确保PC端不显示任何移动导航相关元素 */
  .mobile-nav-wrapper,
  .mobile-nav,
  .mobile-nav-backdrop,
  button[class*="mobile-nav"],
  div[class*="mobile-nav"] {
    display: none !important;
    opacity: 0 !important;
    visibility: hidden !important;
    pointer-events: none !important;
  }
}

/* 平板设备 (768px 到 991px) */
@media (max-width: 991px) {
  .container {
    max-width: 720px;
  }
  
  .hero-title {
    font-size: 3rem;
  }
  
  .hero-subtitle {
    font-size: 1.2rem;
  }
  
  h1 {
    font-size: 2.5rem;
  }
  
  h2 {
    font-size: 2rem;
  }
  
  h3 {
    font-size: 1.5rem;
  }
  
  .mobile-nav-toggle {
    display: block !important;
    position: fixed !important;
    right: 15px;
    top: 15px;
    z-index: 10003 !important;
    border: 0;
    background: rgba(255, 255, 255, 0.9);
    font-size: 24px;
    transition: all 0.4s;
    outline: none !important;
    line-height: 1;
    cursor: pointer;
    text-align: center;
    width: 44px;
    height: 44px;
    padding: 8px;
    border-radius: 4px;
    color: var(--primary-color);
    visibility: visible !important;
    opacity: 1 !important;
  }
  
  .desktop-nav {
    display: none;
  }
  
  .mobile-nav-wrapper {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    visibility: hidden;
    z-index: 10000;
    pointer-events: none;
    overflow: hidden;
  }
  
  .mobile-nav-active .mobile-nav-wrapper {
    visibility: visible;
    pointer-events: auto;
    z-index: 10000;
  }
  
  .mobile-nav {
    position: fixed;
    top: 0;
    right: 0;
    width: 300px;
    height: 100%;
    background: #ffffff;
    padding: 80px 30px 30px;
    transform: translateX(100%);
    transition: transform 0.4s ease, visibility 0.4s ease;
    z-index: 10001;
    box-shadow: -2px 0 10px rgba(0, 0, 0, 0.1);
    visibility: hidden;
    display: flex;
    flex-direction: column;
    overflow-y: auto;
    overflow-x: hidden;
  }
  
  .mobile-nav-active .mobile-nav {
    transform: translateX(0);
    visibility: visible;
  }
  
  .mobile-nav ul {
    list-style: none;
    padding: 0;
    margin: 0;
  }
  
  .mobile-nav li {
    opacity: 0;
    transform: translateX(20px);
    transition: opacity 0.3s ease, transform 0.3s ease;
    transition-delay: calc(0.1s * var(--item-index, 0));
  }
  
  .mobile-nav-active .mobile-nav li {
    opacity: 1;
    transform: translateX(0);
  }
  
  .mobile-nav a {
    display: block;
    padding: 12px 0;
    color: #333333;
    font-size: 18px;
    text-decoration: none;
    transition: transform 0.3s ease;
  }
  
  .mobile-nav a:hover {
    transform: translateX(10px);
    color: #007bff;
  }
  
  .close-mobile-nav {
    position: absolute;
    top: 20px;
    right: 20px;
    background: none;
    border: none;
    font-size: 24px;
    color: #333333;
    cursor: pointer;
    padding: 10px;
    z-index: 10002;
    transition: transform 0.3s ease;
  }
  
  .close-mobile-nav:hover {
    transform: rotate(90deg);
  }
  
  .mobile-nav-backdrop {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: 9999;
  }
  
  .mobile-nav-active .mobile-nav-backdrop {
    opacity: 1;
  }
  
  .nav-menu {
    display: none;
  }
  
  /* Hero区域移动端优化 */
  .hero h1 {
    font-size: 2.5rem !important;
    line-height: 1.2;
  }
  
  .hero p {
    font-size: 1.1rem !important;
  }
  
  /* 内容区块间距优化 */
  .section {
    padding: 60px 0;
  }
  
  .section-title {
    margin-bottom: 40px;
  }
  
  /* 卡片布局优化 */
  .blog-card,
  .case-study-card {
    margin-bottom: 30px;
  }
  
  /* 表单元素优化 */
  input,
  textarea,
  select {
    font-size: 16px !important; /* 防止iOS自动缩放 */
  }
  
  /* 按钮尺寸优化 */
  .btn {
    padding: 12px 25px;
    font-size: 1rem;
  }
  
  .about-content, 
  .case-study-card {
    flex-direction: column;
  }
  
  .about-image,
  .case-study-image {
    margin-bottom: var(--spacing-md);
    width: 100%;
  }
  
  .about-image img {
    max-width: 100%;
    height: auto;
    margin: 0 auto;
  }
  
  .blog-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  /* 案例研究页面响应式 */
  .featured-case {
    flex-direction: column;
  }
  
  .featured-case-image, 
  .featured-case-content {
    width: 100%;
  }
  
  .case-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .filter-wrapper {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .filter-group {
    width: 100%;
  }
  
  .filter-group select {
    flex: 1;
  }
  
  .search-box {
    width: 100%;
  }
  
  .search-box input {
    flex: 1;
  }
  
  .case-stats {
    justify-content: center;
  }
  
  .solution-item {
    flex-direction: column;
  }
  
  .process-diagram {
    flex-direction: column;
  }
  
  .result-highlight {
    grid-template-columns: 1fr;
  }
  
  .results-container {
    flex-direction: column;
  }
  
  .insights-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .related-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  /* 客户评价轮播在平板上的样式调整 */
  .client-testimonials {
  }
  
  .testimonial-swiper {
    position: relative;
    padding-bottom: 60px;
  }
  
  .testimonial-swiper .swiper-pagination {
    bottom: 15px;
  }
  
  .testimonial-swiper .swiper-button-next,
  .testimonial-swiper .swiper-button-prev {
    display: flex !important; 
    top: calc(50% - 30px);
    background-color: rgba(0, 0, 0, 0.2);
    border-radius: 50%;
    width: 40px;
    height: 40px;
    --swiper-navigation-size: 18px;
  }
}

/* 手机设备 (576px 到 767px) */
@media (max-width: 767px) {
  .container {
    max-width: 540px;
  }
  
  .section {
    padding: var(--spacing-lg) 0;
  }
  
  .hero {
    min-height: 600px !important;
  }
  
  .hero-title {
    font-size: 2.5rem !important;
  }
  
  .hero-subtitle {
    font-size: 1.1rem !important;
  }
  
  .blog-grid {
    grid-template-columns: 1fr;
  }
  
  .footer-content {
    flex-direction: column;
  }
  
  .footer-logo, 
  .footer-links {
    margin-bottom: var(--spacing-md);
  }
  
  /* 案例研究页面响应式 */
  .case-grid {
    grid-template-columns: 1fr;
  }
  
  .case-meta {
    flex-direction: column;
    gap: var(--spacing-sm);
  }
  
  .challenges-grid {
    grid-template-columns: 1fr;
  }
  
  .insights-grid {
    grid-template-columns: 1fr;
  }
  
  .related-grid {
    grid-template-columns: 1fr;
  }
  
  .testimonial-slider {
    flex-direction: column;
  }
  
  .testimonial-box .testimonial-content {
    font-size: 1.2rem;
  }
  
  .testimonial-box .testimonial-author {
    flex-direction: column;
    text-align: center;
  }
  
  /*
  .mobile-nav-toggle {
    top: 10px;
    right: 10px;
  }
  */
  
  .mobile-nav {
    width: 280px;
    padding: 70px 25px 25px;
  }
  
  /* DIAGNOSTIC: Temporarily comment out aggressive hiding rules */
  /*
  .case-navigation-buttons {
    display: none !important;
  }
  
  .case-card .swiper-button-next,
  .case-card .swiper-button-prev {
    display: none !important;
  }
  
  .case-study-navigation {
    display: none !important;
  }
  */
  
  .case-study-content {
    padding: 20px;
    width: 100%;
  }
  
  /* DIAGNOSTIC: Temporarily comment out aggressive hiding rules */
  /*
  .blog-navigation-buttons,
  .blog-slider-nav,
  .blog-nav-buttons,
  .blog-card .navigation-arrows,
  .insights-navigation,
  a.swiper-button-next,
  a.swiper-button-prev,
  .navigation-arrow,
  .arrow-left,
  .arrow-right,
  .more-insights-nav,
  .more-insights-navigation {
    display: none !important;
  }
  
  a[href*="swiper"] {
    display: none !important;
  }
  
  [class*="arrow"],
  [class*="nav-button"],
  .fa-chevron-left,
  .fa-chevron-right,
  .fa-angle-left,
  .fa-angle-right {
    display: none !important;
  }
  */
  
  .blog-content {
    padding: 20px;
    width: 100%;
  }
  
  /* DIAGNOSTIC: Temporarily comment out aggressive hiding rules */
  /*
  .swiper-button-next,
  .swiper-button-prev,
  .carousel-control-next,
  .carousel-control-prev,
  a.carousel-control-next,
  a.carousel-control-prev {
    display: none !important;
    opacity: 0 !important;
    visibility: hidden !important;
    pointer-events: none !important;
  }
  
  .case-study-card a[class*="control"],
  .case-study-card div[class*="control"],
  .case-study-card span[class*="control"],
  .case-study-card button[class*="control"],
  .case-study-card i[class*="control"],
  .swiper-navigation,
  .swiper-pagination {
    display: none !important;
    opacity: 0 !important;
    visibility: hidden !important;
  }
  
  a.chevron-left,
  a.chevron-right,
  a.angle-left,
  a.angle-right,
  a > i.fa-chevron-left,
  a > i.fa-chevron-right,
  a > i.fa-angle-left,
  a > i.fa-angle-right {
    display: none !important;
  }
  */

  /* 客户评价轮播在手机上的样式调整 */
  .client-testimonials {
  }

  .testimonial-swiper {
    position: relative; 
    padding-bottom: 70px; 
    /* overflow: hidden; /* Keep or adjust */
  }

  .testimonial-swiper .swiper-pagination {
    display: block !important; 
    position: absolute;
    bottom: 10px; /* Position pagination near the bottom */
    left: 50%;
    transform: translateX(-50%);
    width: auto;
    z-index: 10; 
  }

  .testimonial-swiper .swiper-button-next,
  .testimonial-swiper .swiper-button-prev {
    display: flex !important; 
    position: absolute;
    /* Remove vertical centering */
    /* top: 50%; */ 
    /* transform: translateY(-50%); */
    
    /* Position buttons near the bottom */
    bottom: 30px; /* Adjust this value as needed */
    
    z-index: 10;
    width: 30px; 
    height: 30px; 
    background-color: rgba(0, 0, 0, 0.3); 
    border-radius: 50%;
    color: white;
    --swiper-navigation-size: 16px; 
  }

  .testimonial-swiper .swiper-button-prev {
    left: 15px; /* Adjust horizontal position */
  }

  .testimonial-swiper .swiper-button-next {
    right: 15px; /* Adjust horizontal position */
  }
  
  /* 移除或注释掉该媒体查询内其他强制隐藏Swiper控件的规则 */
  /* 例如: 
  .case-navigation-buttons,
  .case-card .swiper-button-next, 
  ... 
  .swiper-button-next, 
  .swiper-button-prev { display: none !important; } 
  之类的规则，如果它们影响到了 .testimonial-swiper */
}

/* 小型手机设备 (小于 576px) */
@media (max-width: 575px) {
  .container {
    width: 100%;
    padding: 0 15px;
  }
  
  .hero-title {
    font-size: 2rem !important;
  }
  
  .hero-buttons .btn {
    display: block;
    width: 100%;
    margin-bottom: var(--spacing-sm);
    text-align: center;
  }
  
  h1 {
    font-size: 2rem;
  }
  
  h2 {
    font-size: 1.8rem;
  }
  
  h3 {
    font-size: 1.3rem;
  }
  
  p {
    font-size: 1rem;
    line-height: 1.6;
  }
  
  .contact-form {
    padding: var(--spacing-md);
  }
  
  .social-icons {
    justify-content: center;
  }
  
  /* 案例研究页面响应式 */
  .page-title h1 {
    font-size: 1.8rem;
  }
  
  .page-title p {
    font-size: 1rem;
  }
  
  .case-stats {
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-sm);
  }
  
  .stat-value {
    font-size: 1.5rem;
  }
  
  .solution-number {
    font-size: 2rem;
  }
  
  .timeline-item {
    flex-direction: column;
  }
  
  .timeline-date {
    width: 100%;
    margin-bottom: var(--spacing-sm);
  }
  
  .breadcrumbs {
    font-size: 0.8rem;
  }
  
  /* 再次确保导航按钮在更小屏幕上也不显示 - 这部分规则过于宽泛，注释掉以允许下方特定样式生效 */
  /*
  .case-navigation-buttons,
  .case-card .swiper-button-next,
  .case-card .swiper-button-prev,
  .case-study-navigation,
  .blog-navigation-buttons,
  .blog-slider-nav,
  .blog-nav-buttons,
  .blog-card .navigation-arrows,
  .insights-navigation,
  a.swiper-button-next, 
  a.swiper-button-prev, 
  .navigation-arrow,
  .arrow-left,
  .arrow-right,
  .more-insights-nav,
  .more-insights-navigation,
  [class*="arrow"],
  [class*="nav-button"],
  .fa-chevron-left,
  .fa-chevron-right,
  .fa-angle-left,
  .fa-angle-right {
    display: none !important;
  }
  */
  
  /* 针对小屏幕确保所有导航元素隐藏 - 这部分规则过于宽泛，注释掉以允许下方特定样式生效 */
  /* 
  .swiper-button-next,
  .swiper-button-prev,
  .carousel-control-next,
  .carousel-control-prev,
  a.carousel-control-next,
  a.carousel-control-prev,
  .case-study-card a[class*="control"],
  .case-study-card div[class*="control"],
  .case-study-card span[class*="control"],
  .case-study-card button[class*="control"],
  .case-study-card i[class*="control"] {
    display: none !important;
    opacity: 0 !important;
    visibility: hidden !important;
    pointer-events: none !important;
  }
  */

  /* 确保在最小屏幕上样式依然生效 */
  .client-testimonials {
  }
  
  .testimonial-swiper {
    position: relative; 
    padding-bottom: 60px; 
    /* overflow: hidden; /* Keep or adjust */
  }
  
  .testimonial-swiper .swiper-pagination {
      bottom: 8px; /* Position pagination near the bottom */
      z-index: 10;
      /* Keep other styles */
  }
  
  .testimonial-swiper .swiper-button-next,
  .testimonial-swiper .swiper-button-prev {
      /* Remove vertical centering */
      /* top: 50%; */
      /* transform: translateY(-50%); */
      
      /* Position buttons near the bottom */
      bottom: 25px; /* Adjust this value as needed */

      /* Keep other styles (size, etc.) */
      width: 28px;
      height: 28px;
      --swiper-navigation-size: 14px;
      z-index: 10;
      /* Keep background, color, border-radius */
  }
  .testimonial-swiper .swiper-button-prev { left: 10px; /* Adjust horizontal position */ }
  .testimonial-swiper .swiper-button-next { right: 10px; /* Adjust horizontal position */ }
  
  /* ... existing code ... */
}

/* 打印样式 - 可选 */
@media print {
  .header,
  .footer,
  .hero-cta,
  .contact-form,
  .social-icons {
    display: none;
  }
  
  body {
    font-size: 12pt;
    line-height: 1.5;
    color: #000;
    background: #fff;
  }
  
  a {
    color: #000;
    text-decoration: underline;
  }
  
  .container {
    max-width: 100%;
    width: 100%;
  }
  
  h1, h2, h3, h4, h5, h6 {
    page-break-after: avoid;
    page-break-inside: avoid;
  }
  
  img {
    max-width: 100% !important;
    page-break-inside: avoid;
  }
  
  .blog-card,
  .case-study-card,
  .case-card {
    page-break-inside: avoid;
    border: 1px solid #ddd;
    box-shadow: none;
  }
} 