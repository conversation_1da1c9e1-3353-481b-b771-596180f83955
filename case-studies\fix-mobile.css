/* 修复移动导航和轮播显示问题 */
html, body {
    overflow-x: hidden;
    position: relative;
    width: 100%;
}

/* 修复移动导航问题 */
@media (max-width: 991px) {
    .mobile-nav-toggle {
        display: block !important;
        position: absolute !important;
        right: 15px;
        top: 15px;
        z-index: 9999 !important;
        visibility: visible !important;
        opacity: 1 !important;
    }
    
    .mobile-nav-wrapper {
        display: block !important; 
    }
    
    .header {
        overflow-x: hidden;
    }
    
    .navbar {
        position: relative;
    }
    
    .mobile-nav {
        visibility: hidden;
        transition: transform 0.4s ease, visibility 0.4s ease;
        z-index: 10001;
    }
    
    .mobile-nav-active .mobile-nav {
        visibility: visible;
    }
}

/* 修复客户评价轮播 */
.testimonial-swiper {
    overflow: hidden;
    margin: 0 auto;
    position: relative;
    padding-bottom: 50px;
}

.swiper-wrapper {
    height: auto !important;
}

.swiper-slide {
    height: auto;
}

.testimonial {
    padding: 20px;
    height: 100%;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.05);
}

.swiper-pagination {
    position: relative;
    bottom: 0 !important;
    margin-top: 15px;
}

@media (max-width: 767px) {
    .swiper-button-next, 
    .swiper-button-prev {
        display: none;
    }
    
    .testimonial-swiper {
        padding-bottom: 40px;
    }
    
    .client-testimonials .container {
        width: 90%;
        max-width: none;
    }
}