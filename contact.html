<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>联系我 - 张洁贞的个人品牌网站</title>
    <meta name="description" content="联系张洁贞，了解更多关于矿业信息化解决方案的专业咨询。">
    
    <!-- DNS预解析和预连接 -->
    <link rel="dns-prefetch" href="//cdnjs.cloudflare.com">
    <link rel="dns-prefetch" href="//cdn.bootcdn.net">
    <link rel="preconnect" href="//cdnjs.cloudflare.com" crossorigin>
    <link rel="preconnect" href="//cdn.bootcdn.net" crossorigin>
    
    <!-- 预加载关键资源 -->
    <link rel="preload" href="assets/css/main.css" as="style">
    
    <!-- 关键CSS -->
    <style>
        /* 首屏关键样式 */
        .contact-hero {
            min-height: 60vh;
            background: linear-gradient(135deg, #1a237e, #0d47a1);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            position: relative;
            overflow: hidden;
        }
        .contact-form {
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            padding: 2rem;
            max-width: 600px;
            width: 90%;
            margin: -100px auto 50px;
            position: relative;
            z-index: 2;
        }
    </style>
    
    <!-- 异步加载非关键CSS -->
    <link rel="stylesheet" href="assets/css/main.css" media="print" onload="this.media='all'">
    <noscript>
        <link rel="stylesheet" href="assets/css/main.css">
    </noscript>
    
    <!-- 性能优化类 -->
    <link rel="stylesheet" href="assets/css/performance.css">
    
    <!-- 进度条 -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/nprogress/0.2.0/nprogress.min.js"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/nprogress/0.2.0/nprogress.min.css">
    
    <!-- CSS 文件 -->
    <link rel="stylesheet" href="assets/css/main.css">
    <link rel="stylesheet" href="assets/css/responsive.css">
    <link rel="stylesheet" href="assets/css/components.css">
    <link rel="stylesheet" href="assets/css/page-specific.css">
    
    <!-- 网站图标 -->
    <link rel="icon" href="assets/images/favicon.ico">
    
    <!-- Font Awesome 图标库 -->
    <link rel="stylesheet" href="https://cdn.bootcdn.net/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    
    <!-- AOS动画库 -->
    <link rel="stylesheet" href="https://cdn.bootcdn.net/ajax/libs/aos/2.3.4/aos.css">
    <script src="https://cdn.bootcdn.net/ajax/libs/aos/2.3.4/aos.js" defer></script>
</head>
<body>
    <!-- 顶部导航栏 -->
    <header class="header">
        <div class="container">
            <nav class="navbar">
                <a href="index.html" class="logo">张<span>洁贞</span><span class="logo-subtitle">矿业信息化专家</span></a>
                <ul class="nav-menu">
                    <li class="nav-item"><a href="index.html" class="nav-link">首页</a></li>
                    <li class="nav-item"><a href="about.html" class="nav-link">关于我</a></li>
                    <li class="nav-item"><a href="blog/index.html" class="nav-link">洞见</a></li>
                    <li class="nav-item"><a href="case-studies/index.html" class="nav-link">案例</a></li>
                    <li class="nav-item"><a href="contact.html" class="nav-link active">联系</a></li>
                    <li class="nav-item"><a href="contact.html" class="nav-contact-btn"><i class="fas fa-headset"></i>咨询我</a></li>
                </ul>
                <button class="mobile-nav-toggle">
                    <i class="fas fa-bars"></i>
                </button>
            </nav>
        </div>
    </header>
    
    <!-- 移动导航菜单 -->
    <div class="mobile-nav-wrapper">
        <div class="mobile-nav">
            <button class="close-mobile-nav">
                <i class="fas fa-times"></i>
            </button>
            <ul>
                <li><a href="index.html">首页</a></li>
                <li><a href="about.html">关于我</a></li>
                <li><a href="blog/index.html">洞见</a></li>
                <li><a href="case-studies/index.html">案例</a></li>
                <li><a href="contact.html" class="active">联系</a></li>
            </ul>
        </div>
        <div class="mobile-nav-backdrop"></div>
    </div>
    
    <!-- 页面标题部分 -->
    <section style="position: relative; background: url('assets/images/background-texture2.webp') center/cover; padding: 120px 0 80px;">
        <div style="position: absolute; top: 0; left: 0; right: 0; bottom: 0; background: linear-gradient(135deg, rgba(26, 54, 93, 0.45), rgba(21, 32, 50, 0.65));"></div>
        <div class="container" style="position: relative; z-index: 2;">
            <h1 style="color: var(--white); margin-bottom: 1rem; font-size: 2.8rem; text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);" data-aos="fade-up" data-aos-delay="100">联系我</h1>
            <p style="font-size: 1.2rem; max-width: 700px; color: rgba(255, 255, 255, 0.95); text-shadow: 0 1px 3px rgba(0, 0, 0, 0.2); line-height: 1.6;" data-aos="fade-up" data-aos-delay="200">让我们一起探讨如何为您的企业提供最佳矿业信息化解决方案</p>
        </div>
    </section>
    
    <!-- 联系页面主要内容 -->
    <main class="optimize-reflow">
        <!-- 联系信息卡片区域 -->
        <section class="contact-cards-section">
            <div class="container">
                <div class="contact-cards-grid">
                    <!-- 主要联系信息卡片 -->
                    <div class="contact-card main-contact" data-aos="fade-up">
                        <div class="contact-item">
                            <div class="contact-icon">
                                <img src="assets/images/svg/email.svg" alt="邮箱">
                            </div>
                            <div class="contact-info">
                                <h3>电子邮箱</h3>
                                <a href="mailto:<EMAIL>"><EMAIL></a>
                            </div>
                        </div>
                        <div class="contact-item">
                            <div class="contact-icon">
                                <img src="assets/images/svg/dianhua.svg" alt="电话">
                            </div>
                            <div class="contact-info">
                                <h3>联系电话</h3>
                                <a href="tel:+8613938155869">+86 139 3815 5869</a>
                            </div>
                        </div>
                        <div class="contact-item">
                            <div class="contact-icon">
                                <img src="assets/images/svg/weixin.svg" alt="微信">
                            </div>
                            <div class="contact-info">
                                <h3>微信号</h3>
                                <p>zhangjiezhen176</p>
                            </div>
                        </div>
                    </div>

                    <!-- 微信二维码卡片 -->
                    <div class="contact-card qr-code" data-aos="fade-up" data-aos-delay="200">
                        <h3>扫码添加微信</h3>
                        <p>扫描下方二维码，直接与我沟通</p>
                        <div class="qr-code-wrapper">
                            <img src="assets/images/wechat-qr.jpg" alt="张洁贞微信二维码">
                        </div>
                    </div>
                </div>

                <!-- 附加信息卡片 -->
                <div class="additional-cards">
                    <!-- 公司地址卡片 -->
                    <div class="contact-card address" data-aos="fade-up" data-aos-delay="300">
                        <div class="card-icon">
                            <i class="fas fa-map-marker-alt"></i>
                        </div>
                        <h3>总部地址</h3>
                        <p>中矿天智信息科技(徐州)有限公司<br>中矿天智信息科技--中国矿业大学</p>
                    </div>

                    <!-- 社交媒体卡片 -->
                    <div class="contact-card social" data-aos="fade-up" data-aos-delay="400">
                        <div class="card-icon">
                            <i class="fas fa-share-alt"></i>
                        </div>
                        <h3>社交媒体</h3>
                        <p>关注我的社交媒体账号，获取更多矿业信息化资讯</p>
                        <div class="social-icons">
                            <a href="#" class="social-icon douyin">
                                <img src="assets/images/svg/douyin.svg" alt="抖音">
                            </a>
                            <a href="#" class="social-icon xiaohongshu">
                                <img src="assets/images/svg/xiaohongshu.svg" alt="小红书">
                            </a>
                            <a href="#" class="social-icon weixin">
                                <img src="assets/images/svg/weixin.svg" alt="微信">
                            </a>
                            <a href="#" class="social-icon podcast">
                                <img src="assets/images/svg/boke.svg" alt="播客">
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </main>
    
    <!-- 常见问题 FAQ -->
    <section class="section" style="background-color: var(--light-gray);">
        <div class="container">
            <div class="section-title" data-aos="fade-up" data-aos-delay="100">
                <h2>常见问题</h2>
            </div>
            
            <div style="max-width: 800px; margin: 0 auto;">
                <div style="margin-bottom: 2rem;" data-aos="fade-up" data-aos-delay="200">
                    <h3>中矿天智提供哪些类型的解决方案？</h3>
                    <p>中矿天智主要提供煤矿安全生产监测系统、智能开采平台、矿井通风优化系统、生产调度管理平台等智能矿山解决方案。我们的产品结合物联网、大数据、人工智能等先进技术，为煤矿企业提供全方位的数字化转型服务。</p>
                </div>
                
                <div style="margin-bottom: 2rem;" data-aos="fade-up" data-aos-delay="300">
                    <h3>这些智能化系统能给煤矿企业带来哪些价值？</h3>
                    <p>我们的智能化系统能够显著提高煤矿安全水平，实现安全事故率平均降低45%；提升生产效率30%-50%；降低运营成本15%-25%；延长设备使用寿命20%-30%。此外，还能帮助企业实现数字化管理，提高决策效率与准确性。</p>
                </div>
                
                <div style="margin-bottom: 2rem;" data-aos="fade-up" data-aos-delay="400">
                    <h3>您们的服务范围覆盖哪些地区？</h3>
                    <p>我们主要服务于陕西、山西、内蒙古、新疆、山东等煤炭资源丰富的省份，已成功为这些地区的多家大型煤矿集团提供了智能化解决方案。同时，我们也有能力为全国各地的煤矿企业提供服务。</p>
                </div>
                
                <div style="margin-bottom: 2rem;" data-aos="fade-up" data-aos-delay="500">
                    <h3>实施智能矿山解决方案需要多长时间？</h3>
                    <p>根据项目规模和复杂程度，实施周期一般为3-12个月。我们会根据客户需求和矿区实际情况，制定分阶段的实施计划，确保系统平稳上线与稳定运行。同时，我们提供全流程技术支持和培训服务，帮助客户快速掌握系统使用方法。</p>
                </div>
                
                <div data-aos="fade-up" data-aos-delay="600">
                    <h3>采购智能矿山解决方案的最佳流程是什么？</h3>
                    <p>建议先与我们的专业顾问进行需求沟通，了解贵方煤矿的具体情况和痛点问题。我们会安排技术团队进行现场勘察和需求分析，然后提供定制化的解决方案和详细报价。确认方案后，我们将按照合同约定进行系统设计、开发、安装、调试和培训等工作，最终交付满足需求的智能矿山系统。</p>
                </div>
            </div>
        </div>
    </section>
    
    <!-- 页脚 -->
    <footer class="footer" data-aos="fade-up" data-aos-duration="800">
        <div class="container">
            <div class="footer-content">
                <div class="footer-logo">
                    <h3>张<span>洁贞</span></h3>
                    <p>中矿天智信息科技(徐州)有限公司高级销售经理，专注矿山智能化解决方案。</p>
                    <div class="social-icons">
                        <a href="#" class="social-icon" title="抖音">
                            <img src="assets/images/svg/douyin.svg" alt="抖音" class="social-media-icon">
                        </a>
                        <a href="#" class="social-icon" title="小红书">
                            <img src="assets/images/svg/xiaohongshu.svg" alt="小红书" class="social-media-icon">
                        </a>
                        <a href="#" class="social-icon" title="微信">
                            <img src="assets/images/svg/weixin.svg" alt="微信" class="social-media-icon">
                        </a>
                        <a href="#" class="social-icon" title="播客">
                            <img src="assets/images/svg/boke.svg" alt="播客" class="social-media-icon">
                        </a>
                    </div>
                </div>
                
                <div class="footer-links">
                    <h4>网站导航</h4>
                    <ul>
                        <li><a href="index.html">首页</a></li>
                        <li><a href="about.html">关于我</a></li>
                        <li><a href="blog/index.html">专业洞见</a></li>
                        <li><a href="case-studies/index.html">成功案例</a></li>
                        <li><a href="contact.html">联系方式</a></li>
                    </ul>
                </div>
                
                <div class="footer-links">
                    <h4>联系方式</h4>
                    <ul>
                        <li>
                            <img src="assets/images/svg/email.svg" alt="邮箱" class="footer-contact-icon">
                            <a href="mailto:<EMAIL>"><EMAIL></a>
                        </li>
                        <li>
                            <img src="assets/images/svg/dianhua.svg" alt="电话" class="footer-contact-icon">
                            <a href="tel:+8613938155869">+86 139 3815 5869</a>
                        </li>
                        <li>
                            <img src="assets/images/svg/weixin.svg" alt="微信" class="footer-contact-icon">
                            <span class="wechat-id">zhangjiezhen176</span>
                        </li>
                    </ul>
                </div>
            </div>
            
            <div class="footer-bottom">
                <p>&copy; 2025 张洁贞 - 矿业信息化解决方案专家.</p>
            </div>
        </div>
    </footer>
    
    <!-- JavaScript 文件 -->
    <script src="assets/js/utils.js" defer></script>
    <script src="assets/js/navigation.js" defer></script>
    <script src="assets/js/page-specific.js" defer></script>
    
    <script>
        // 页面加载进度条
        NProgress.configure({ showSpinner: false });
        NProgress.start();
        window.addEventListener('load', function() {
            NProgress.done();
        });

        // 表单验证和提交
        document.addEventListener('DOMContentLoaded', function() {
            const form = document.getElementById('contactForm');
            if (form) {
                form.addEventListener('submit', async function(e) {
                    e.preventDefault();
                    // 表单验证和提交逻辑
                });
            }
        });

        // 初始化AOS
        document.addEventListener('DOMContentLoaded', function() {
            AOS.init({
                duration: 800,
                easing: 'ease-in-out',
                once: true,
                disable: window.innerWidth < 768
            });
        });
    </script>

    <style>
        /* 页面标题样式 */
        .page-title {
            position: relative;
            padding: 120px 0;
            color: #fff;
            text-align: center;
            overflow: hidden;
        }

        .page-title-bg {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-image: url('assets/images/background-texture4.webp');
            background-size: cover;
            background-position: center;
            background-repeat: no-repeat;
        }

        .page-title-bg::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 4px;
            background: linear-gradient(90deg, rgba(255,255,255,0.1), rgba(255,255,255,0.3), rgba(255,255,255,0.1));
            z-index: 2;
        }

        .page-title .container {
            position: relative;
            z-index: 2;
        }

        .page-title h1 {
            font-size: 3rem;
            font-weight: 700;
            margin-bottom: 1rem;
            color: #fff;
        }

        .page-title p {
            font-size: 1.2rem;
            max-width: 800px;
            margin: 0 auto;
            opacity: 0.9;
        }

        /* 移除重复的联系hero部分 */
        .contact-hero {
            display: none;
        }

        /* 调整卡片区域的上边距 */
        .contact-cards-section {
            padding-top: 80px;
        }

        .contact-cards-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-bottom: 40px;
        }

        .contact-card {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.08);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .contact-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 40px rgba(0,0,0,0.12);
        }

        .main-contact {
            display: flex;
            flex-direction: column;
            gap: 25px;
        }

        .contact-item {
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .contact-icon {
            width: 56px;
            height: 56px;
            background: var(--primary-color);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-shrink: 0;
        }

        .contact-icon img {
            width: 24px;
            height: 24px;
            filter: brightness(0) invert(1);
        }

        .contact-info h3 {
            font-size: 1.1rem;
            color: var(--dark-gray);
            margin-bottom: 5px;
        }

        .contact-info a,
        .contact-info p {
            color: var(--primary-color);
            font-size: 1.1rem;
            text-decoration: none;
            margin: 0;
            transition: color 0.3s ease;
        }

        .contact-info a:hover {
            color: var(--secondary-color);
        }

        .qr-code {
            text-align: center;
        }

        .qr-code h3 {
            font-size: 1.3rem;
            color: var(--dark-gray);
            margin-bottom: 10px;
        }

        .qr-code p {
            color: var(--medium-gray);
            margin-bottom: 20px;
        }

        .qr-code-wrapper {
            width: 220px;
            height: 220px;
            margin: 0 auto;
            padding: 10px;
            background: white;
            border-radius: 12px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        }

        .qr-code-wrapper img {
            width: 100%;
            height: 100%;
            object-fit: contain;
            border-radius: 8px;
        }

        .additional-cards {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
        }

        .address, .social {
            text-align: center;
        }

        .card-icon {
            width: 60px;
            height: 60px;
            background: var(--primary-color);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 20px;
        }

        .card-icon i {
            font-size: 24px;
            color: white;
        }

        .social-icons {
            display: flex;
            gap: 15px;
            justify-content: center;
            margin-top: 20px;
        }

        .social-icon {
            width: 45px;
            height: 45px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: transform 0.3s ease;
            background: rgba(26, 54, 93, 0.1);
        }

        .social-icon:hover {
            transform: scale(1.1);
            background: rgba(26, 54, 93, 0.9);
        }

        .social-icon img {
            width: 22px;
            height: 22px;
            transition: filter 0.3s ease;
        }

        .social-icon:hover img {
            filter: brightness(0) invert(1);
        }

        /* 社交图标默认颜色 */
        .social-icon.douyin img { filter: invert(23%) sepia(92%) saturate(6622%) hue-rotate(357deg) brightness(97%) contrast(120%); }
        .social-icon.xiaohongshu img { filter: invert(36%) sepia(45%) saturate(3980%) hue-rotate(322deg) brightness(100%) contrast(100%); }
        .social-icon.weixin img { filter: invert(58%) sepia(86%) saturate(3950%) hue-rotate(122deg) brightness(96%) contrast(101%); }
        .social-icon.podcast img { filter: invert(10%) sepia(100%) saturate(5623%) hue-rotate(246deg) brightness(102%) contrast(143%); }

        @media (max-width: 992px) {
            .contact-cards-grid {
                grid-template-columns: 1fr;
            }

            .qr-code-wrapper {
                width: 180px;
                height: 180px;
            }
        }

        @media (max-width: 768px) {
            .contact-cards-grid {
                grid-template-columns: 1fr;
            }

            .qr-code-wrapper {
                width: 180px;
                height: 180px;
            }
        }
    </style>
</body>
</html> 