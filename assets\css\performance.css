/* 
 * 性能优化专用样式
 * 包含各种性能优化相关的CSS规则
 */

/* 1. 图片延迟加载优化 */
.lazy-load {
  opacity: 0;
  transition: opacity 0.3s ease;
}

.lazy-load.loaded {
  opacity: 1;
}

/* 2. 图片占位符 */
.image-placeholder {
  background-color: var(--light-gray);
  position: relative;
  overflow: hidden;
}

.image-placeholder::before {
  content: '';
  display: block;
  padding-top: 56.25%; /* 16:9 比例 */
}

/* 3. 硬件加速优化 */
.hardware-accelerated {
  transform: translateZ(0);
  will-change: transform;
  backface-visibility: hidden;
}

/* 4. 滚动性能优化 */
.scroll-container {
  -webkit-overflow-scrolling: touch;
  scroll-behavior: smooth;
  overscroll-behavior: contain;
}

/* 5. 动画性能优化 */
.optimize-animation {
  will-change: transform, opacity;
  transform: translateZ(0);
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}

/* 6. 渐进式加载 */
.progressive-load {
  opacity: 0;
  transform: translateY(20px);
  transition: opacity 0.5s ease, transform 0.5s ease;
}

.progressive-load.visible {
  opacity: 1;
  transform: translateY(0);
}

/* 7. 响应式图片容器 */
.responsive-image-container {
  position: relative;
  width: 100%;
  height: 0;
  padding-bottom: 56.25%;
  background-color: var(--light-gray);
  overflow: hidden;
}

.responsive-image-container img {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* 8. 移动端优化 */
@media (max-width: 768px) {
  .mobile-optimize {
    transform: translateZ(0);
    -webkit-backface-visibility: hidden;
    backface-visibility: hidden;
  }
  
  .disable-hover {
    pointer-events: none;
  }
  
  .touch-scroll {
    -webkit-overflow-scrolling: touch;
  }
}

/* 9. 减少重排重绘 */
.optimize-reflow {
  transform: translateZ(0);
  contain: content;
  content-visibility: auto;
  contain-intrinsic-size: 0 500px;
}

/* 10. 动画降级处理 */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

/* 11. 关键渲染路径优化 */
.critical-render {
  content-visibility: auto;
  contain-intrinsic-size: 0 500px;
}

/* 12. 字体加载优化 */
.font-optimization {
  font-display: swap;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* 13. 视口外内容优化 */
.viewport-content {
  content-visibility: auto;
  contain: style layout paint;
}

/* 14. 动画帧率优化 */
.frame-rate-optimization {
  transform: translateZ(0);
  backface-visibility: hidden;
  perspective: 1000px;
  transform-style: preserve-3d;
}

/* 15. 触摸设备优化 */
.touch-device {
  touch-action: manipulation;
  -webkit-tap-highlight-color: transparent;
  user-select: none;
}

/* 16. 骨架屏样式 - 适用于中国网站流行的加载效果 */
.content-skeleton {
  width: 100%;
  padding: 15px;
}

.skeleton {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
  height: 24px;
  margin-bottom: 8px;
  border-radius: 4px;
}

.skeleton-image {
  width: 100%;
  height: 0;
  padding-bottom: 56.25%;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
  border-radius: 4px;
}

@keyframes skeleton-loading {
  0% { background-position: 200% 0; }
  100% { background-position: -200% 0; }
}

/* 17. 渐进式图片加载样式 */
.progressive-image {
  transition: filter 0.3s ease, opacity 0.3s ease;
}

.progressive-image.loading {
  filter: blur(10px);
  opacity: 0.6;
}

.progressive-image.loaded {
  filter: blur(0);
  opacity: 1;
}

/* 18. 微信浏览器适配样式 */
.wechat-optimize {
  /* 修复微信浏览器中的常见问题 */
  -webkit-overflow-scrolling: touch;
  -webkit-tap-highlight-color: transparent;
}

/* 微信中的特殊交互元素 */
.wechat-btn {
  user-select: none;
  -webkit-user-select: none;
  appearance: none;
  -webkit-appearance: none;
  border-radius: 4px;
  font-size: 16px; /* 避免微信中自动缩放 */
}

/* 19. UC、QQ等中国主流浏览器优化 */
.china-browser-fix {
  /* 修复橡皮筋效果问题 */
  overscroll-behavior: none;
  
  /* 修复字体大小自动调整问题 */
  text-size-adjust: 100%;
  -webkit-text-size-adjust: 100%;
  
  /* 修复点击延迟问题 */
  touch-action: manipulation;
}

/* 20. 网络环境感知样式 */
.slow-network-mode {
  /* 在检测到慢网络时启用的样式 */
  display: none;
}

body.slow-connection .slow-network-mode {
  display: block;
}

body.slow-connection .high-bandwidth-content {
  display: none;
}

/* 21. 预加载动画 */
.preloader {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(255, 255, 255, 0.98);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
  transition: opacity 0.5s ease, visibility 0.5s ease;
}

.preloader.hidden {
  opacity: 0;
  visibility: hidden;
}

.preloader-spinner {
  width: 50px;
  height: 50px;
  border: 5px solid rgba(0, 0, 0, 0.1);
  border-radius: 50%;
  border-top-color: var(--primary-color, #1A365D);
  animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

/* 22. 百度/360等中国搜索引擎优化 */
.china-seo-optimized {
  /* 针对百度、360等中国搜索引擎的可见性优化 */
  visibility: visible !important;
  display: block !important;
  opacity: 1 !important;
}

/* 23. 图片加载失败后的样式 */
.img-fallback {
  position: relative;
}

.img-fallback::before {
  content: '图片加载失败';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #f5f5f5;
  color: #999;
  font-size: 14px;
  opacity: 0;
  visibility: hidden;
}

.img-fallback.error::before {
  opacity: 1;
  visibility: visible;
}

.img-fallback.error img {
  opacity: 0;
} 