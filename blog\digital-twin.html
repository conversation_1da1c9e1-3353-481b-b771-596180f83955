<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数字孪生技术在现代煤矿中的应用前景 - 张洁贞 | 矿业信息化解决方案专家</title>
    <meta name="description" content="数字孪生技术为煤矿提供了虚实结合的智能管理新模式，实现了从被动应对到主动预测的转变。本文探讨了该技术在煤矿领域的创新应用。">
    
    <!-- CSS 文件 -->
    <link rel="stylesheet" href="../assets/css/main.css">
    <link rel="stylesheet" href="../assets/css/responsive.css">
    
    <!-- 网站图标 -->
    <link rel="icon" href="../assets/images/favicon.ico">
    
    <!-- Font Awesome 图标库 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    
    <!-- AOS动画库 -->
    <link rel="stylesheet" href="https://cdn.bootcdn.net/ajax/libs/aos/2.3.4/aos.css">
    <script src="https://cdn.bootcdn.net/ajax/libs/aos/2.3.4/aos.js" defer></script>
    
    <!-- NProgress加载进度条 -->
    <link rel="stylesheet" href="https://cdn.bootcdn.net/ajax/libs/nprogress/0.2.0/nprogress.min.css">
    <script src="https://cdn.bootcdn.net/ajax/libs/nprogress/0.2.0/nprogress.min.js"></script>
</head>
<body>
    <!-- 顶部导航栏 -->
    <header class="header">
        <div class="container">
            <nav class="navbar">
                <a href="../index.html" class="logo">张<span>洁贞</span><span class="logo-subtitle">矿业信息化专家</span></a>
                <ul class="nav-menu">
                    <li class="nav-item"><a href="../index.html" class="nav-link">首页</a></li>
                    <li class="nav-item"><a href="../about.html" class="nav-link">关于我</a></li>
                    <li class="nav-item"><a href="../blog/index.html" class="nav-link active">洞见</a></li>
                    <li class="nav-item"><a href="../case-studies/index.html" class="nav-link">案例</a></li>
                    <li class="nav-item"><a href="../contact.html" class="nav-link">联系</a></li>
                    <li class="nav-item"><a href="../contact.html" class="nav-contact-btn"><i class="fas fa-headset"></i>咨询我</a></li>
                </ul>
                <button class="mobile-nav-toggle">
                    <i class="fas fa-bars"></i>
                </button>
            </nav>
        </div>
    </header>
    
    <section class="section" style="padding-top: 120px;">
        <div class="container">
            <div style="max-width: 800px; margin: 0 auto;">
                <!-- 文章头部信息 -->
                <div style="margin-bottom: 2rem;">
                    <span style="display: inline-block; padding: 5px 10px; background-color: var(--light-gray); color: var(--primary-color); font-size: 0.8rem; border-radius: 4px; margin-bottom: 15px;">数字孪生</span>
                    <h1 style="font-size: 2.5rem; margin-bottom: 1rem;">数字孪生技术在现代煤矿中的应用前景</h1>
                    <div style="display: flex; align-items: center; margin-bottom: 1.5rem;">
                        <img src="../assets/images/profile-pic.webp" alt="张洁贞" style="width: 50px; border-radius: 50%; margin-right: 15px; object-fit: cover;">
                        <div>
                            <p style="margin: 0; font-weight: 500;">张洁贞</p>
                            <p style="margin: 0; font-size: 0.9rem; color: var(--medium-gray);">2023年10月10日 · 阅读时间: 10分钟</p>
                        </div>
                    </div>
                    <div style="display: flex; gap: 10px; margin-bottom: 1.5rem;">
                        <span style="display: inline-block; padding: 5px 10px; background-color: var(--light-gray); color: var(--medium-gray); font-size: 0.8rem; border-radius: 4px;">#数字孪生</span>
                        <span style="display: inline-block; padding: 5px 10px; background-color: var(--light-gray); color: var(--medium-gray); font-size: 0.8rem; border-radius: 4px;">#智慧矿山</span>
                        <span style="display: inline-block; padding: 5px 10px; background-color: var(--light-gray); color: var(--medium-gray); font-size: 0.8rem; border-radius: 4px;">#虚拟现实</span>
                    </div>
                </div>
                
                <!-- 文章内容 -->
                <div style="margin-bottom: 3rem; line-height: 1.8;">
                    <p style="margin-bottom: 1.5rem;">数字孪生技术为煤矿提供了虚实结合的智能管理新模式，实现了从被动应对到主动预测的转变。作为当前煤矿智能化转型的关键技术，数字孪生通过构建矿井的虚拟镜像，为安全生产和智能决策提供了全新的技术路径。本文将深入探讨数字孪生技术在煤矿领域的理论基础、关键应用场景以及实施路径，帮助煤矿企业更好地把握数字化转型机遇。</p>
                    
                    <h2 style="margin-top: 2.5rem; margin-bottom: 1.5rem;">数字孪生技术的理论基础与核心价值</h2>
                    
                    <p>数字孪生(Digital Twin)技术是一种通过构建数字模型和物理实体之间的映射关系，实现对物理实体的实时监测、分析和控制的技术。它在煤矿领域具有重要的应用价值，包括...</p>
                    
                    <ul style="padding-left: 1.5rem; margin-bottom: 1.5rem;">
                        <li><strong>全面可视化管理：</strong>实现对矿山空间结构、生产过程、设备状态和人员分布的三维可视化展示，打破信息孤岛，为管理决策提供直观依据。</li>
                        <li><strong>实时动态监测：</strong>通过物联网技术采集矿山环境、设备和生产的实时数据，并将其映射到虚拟模型中，实现对矿山运行状态的动态感知。</li>
                        <li><strong>预测性分析：</strong>基于历史数据和实时数据，结合人工智能算法，对矿山安全风险、设备故障和生产异常进行预测，从被动应对转变为主动预防。</li>
                        <li><strong>闭环决策支持：</strong>为矿山管理者提供数据驱动的决策支持，优化资源配置，提高生产效率，降低安全风险。</li>
                        <li><strong>虚实协同运行：</strong>实现虚拟模型与实体矿山的双向数据流动和控制反馈，将虚拟空间的优化策略应用到实际生产中，实现远程自动化控制。</li>
                    </ul>
                    
                    <h2 style="margin-top: 2.5rem; margin-bottom: 1.5rem;">煤矿数字孪生系统的架构设计</h2>
                    
                    <p>构建一个完整的煤矿数字孪生系统，需要从数据采集、模型构建、平台支撑、应用开发等多个层面进行架构设计。根据我在多家煤矿企业的实践经验，一个典型的煤矿数字孪生系统架构包括以下五层：</p>
                    
                    <h3 style="margin-top: 2rem; margin-bottom: 1rem;">1. 感知层：矿山数据全面采集</h3>
                    <p>感知层是数字孪生系统的数据基础，通过各类传感设备和数据接口，实现对矿山物理世界的全方位感知：</p>
                    <ul style="padding-left: 1.5rem; margin-bottom: 1.5rem;">
                        <li><strong>环境监测网络：</strong>采用瓦斯、一氧化碳、风速、温度等传感器，构建井下环境立体监测网络</li>
                        <li><strong>设备状态监测：</strong>通过振动、温度、压力等传感器，实时采集关键设备运行数据</li>
                        <li><strong>人员定位系统：</strong>利用RFID、UWB等技术，精确定位井下人员位置</li>
                        <li><strong>空间数据采集：</strong>结合三维激光扫描、无人机测绘等技术，获取矿山空间数据</li>
                        <li><strong>生产数据集成：</strong>与采煤、掘进、运输等生产系统对接，实时采集生产数据</li>
                    </ul>
                    
                    <figure style="margin: 2rem 0;">
                        <img src="../assets/images/blog/article-image-3.webp" alt="矿山数字孪生模型" style="width: 100%; border-radius: 8px;">
                        <figcaption style="font-size: 0.9rem; color: var(--medium-gray); margin-top: 0.5rem; text-align: center;">矿山数字孪生系统通过多源数据融合，构建矿井的虚拟镜像</figcaption>
                    </figure>
                    
                    <h3 style="margin-top: 2rem; margin-bottom: 1rem;">2. 传输层：高可靠的网络通信</h3>
                    <p>传输层负责将感知层采集的数据安全、高效地传输到计算层，是数字孪生系统的神经网络：</p>
                    <ul style="padding-left: 1.5rem; margin-bottom: 1.5rem;">
                        <li><strong>井下工业以太网：</strong>构建高可靠的井下工业以太网，满足大量数据的高速传输需求</li>
                        <li><strong>5G/6G网络：</strong>在关键区域部署5G/6G网络，支持高带宽、低时延的数据传输</li>
                        <li><strong>边缘计算节点：</strong>在靠近数据源的位置部署边缘计算节点，实现数据的初步处理和筛选</li>
                        <li><strong>网络安全防护：</strong>采用工业防火墙、数据加密等技术，保障数据传输安全</li>
                    </ul>
                    
                    <h3 style="margin-top: 2rem; margin-bottom: 1rem;">3. 计算层：数据处理与模型构建</h3>
                    <p>计算层是数字孪生系统的核心，负责数据处理、存储和模型构建：</p>
                    <ul style="padding-left: 1.5rem; margin-bottom: 1.5rem;">
                        <li><strong>数据预处理：</strong>对采集的原始数据进行清洗、标准化和结构化处理</li>
                        <li><strong>时空数据库：</strong>构建支持时空数据存储和查询的专业数据库</li>
                        <li><strong>三维建模引擎：</strong>基于空间数据构建矿山的高精度三维模型</li>
                        <li><strong>行为规则建模：</strong>构建设备、环境、人员等各类对象的行为模型和关联规则</li>
                        <li><strong>人工智能引擎：</strong>集成机器学习、深度学习等AI算法，支持智能分析和预测</li>
                    </ul>
                    
                    <h3 style="margin-top: 2rem; margin-bottom: 1rem;">4. 平台层：数字孪生运行平台</h3>
                    <p>平台层提供数字孪生系统的运行环境和基础服务，是连接底层数据和上层应用的桥梁：</p>
                    <ul style="padding-left: 1.5rem; margin-bottom: 1.5rem;">
                        <li><strong>可视化引擎：</strong>支持三维场景渲染、数据可视化和交互操作</li>
                        <li><strong>数字孪生管理器：</strong>管理各类数字孪生对象的创建、更新和销毁</li>
                        <li><strong>仿真分析引擎：</strong>支持各类仿真分析，如通风网络分析、灾害扩散模拟等</li>
                        <li><strong>API和服务接口：</strong>提供标准化的API接口，支持应用层的灵活调用</li>
                        <li><strong>权限和安全管理：</strong>实现系统访问控制和数据安全管理</li>
                    </ul>
                    
                    <h3 style="margin-top: 2rem; margin-bottom: 1rem;">5. 应用层：面向业务的场景应用</h3>
                    <p>应用层基于平台层提供的服务，开发各类面向业务的应用场景：</p>
                    <ul style="padding-left: 1.5rem; margin-bottom: 1.5rem;">
                        <li><strong>安全预警应用：</strong>实现风险智能识别和多级预警</li>
                        <li><strong>生产调度应用：</strong>支持生产过程可视化监控和智能调度</li>
                        <li><strong>设备健康管理：</strong>实现设备状态监测和预测性维护</li>
                        <li><strong>智能辅助决策：</strong>为管理层提供数据驱动的决策支持</li>
                        <li><strong>应急指挥应用：</strong>支持应急情况下的可视化指挥和协同处置</li>
                    </ul>
                    
                    <h2 style="margin-top: 2.5rem; margin-bottom: 1.5rem;">数字孪生技术在煤矿的典型应用场景</h2>
                    
                    <p>数字孪生技术在煤矿领域有着广泛的应用前景，以下是几个典型的应用场景：</p>
                    
                    <h3 style="margin-top: 2rem; margin-bottom: 1rem;">1. 智能通风管理</h3>
                    <p>传统煤矿通风系统存在能耗高、调节不灵活等问题。基于数字孪生技术的智能通风系统可以：</p>
                    <ul style="padding-left: 1.5rem; margin-bottom: 1.5rem;">
                        <li><strong>通风网络可视化：</strong>构建矿井通风网络的数字孪生模型，实现三维可视化展示</li>
                        <li><strong>通风参数实时监测：</strong>通过传感器网络实时监测风量、风压、瓦斯浓度等参数</li>
                        <li><strong>通风网络仿真分析：</strong>基于数字模型进行通风网络计算和优化分析</li>
                        <li><strong>智能风量调控：</strong>根据生产需求和环境变化，自动调节局部风量，实现精准送风</li>
                        <li><strong>通风事故模拟推演：</strong>模拟各类通风事故场景，为应急预案提供支持</li>
                    </ul>
                    
                    <h3 style="margin-top: 2rem; margin-bottom: 1rem;">2. 设备全生命周期管理</h3>
                    <p>煤矿设备维护是保障安全生产的重要环节。数字孪生技术可实现设备全生命周期的智能管理：</p>
                    <ul style="padding-left: 1.5rem; margin-bottom: 1.5rem;">
                        <li><strong>设备数字孪生模型：</strong>构建包含几何、物理和行为特性的设备数字模型</li>
                        <li><strong>设备健康状态监测：</strong>通过传感器实时监测设备运行参数，评估健康状态</li>
                        <li><strong>故障预测与诊断：</strong>基于历史数据和AI算法，预测潜在故障并进行诊断</li>
                        <li><strong>智能维保决策：</strong>根据设备状态自动生成维保计划，优化维保资源配置</li>
                        <li><strong>设备性能优化：</strong>通过虚拟测试和仿真，优化设备运行参数，提高性能和寿命</li>
                    </ul>
                    
                    <h3 style="margin-top: 2rem; margin-bottom: 1rem;">3. 灾害预警与应急救援</h3>
                    <p>煤矿安全生产面临瓦斯、水、火、冒顶等多种灾害威胁。数字孪生技术为灾害预警和应急救援提供了新的解决方案：</p>
                    <ul style="padding-left: 1.5rem; margin-bottom: 1.5rem;">
                        <li><strong>多源数据融合分析：</strong>融合地质、环境、生产等多源数据，识别潜在风险</li>
                        <li><strong>灾害演化模拟：</strong>模拟瓦斯爆炸、火灾蔓延等灾害发展过程，评估影响范围</li>
                        <li><strong>智能预警决策：</strong>基于风险评估结果，自动生成多级预警信息</li>
                        <li><strong>可视化应急指挥：</strong>在三维环境中直观展示灾害现场情况，辅助指挥决策</li>
                        <li><strong>最优救援路径规划：</strong>根据灾害态势，自动规划最优救援路径和方案</li>
                    </ul>
                    
                    <blockquote style="margin: 2rem 0; padding: 1.5rem; background: var(--light-gray); border-left: 4px solid var(--secondary-color); font-style: italic;">
                        数字孪生不仅是一种技术，更是一种思维方式的转变。它让我们能够在虚拟世界中先行一步，预见问题、验证方案、优化决策，从而在现实世界中更加从容应对各种挑战。
                    </blockquote>
                    
                    <h3 style="margin-top: 2rem; margin-bottom: 1rem;">4. 智能采掘作业管理</h3>
                    <p>采掘作业是煤矿生产的核心环节，数字孪生技术可显著提升其智能化水平：</p>
                    <ul style="padding-left: 1.5rem; margin-bottom: 1.5rem;">
                        <li><strong>工作面三维重建：</strong>基于激光扫描等技术，实时重建工作面三维模型</li>
                        <li><strong>采掘设备协同控制：</strong>通过数字孪生模型，实现采煤机、支架等设备的协同控制</li>
                        <li><strong>采掘参数优化：</strong>基于地质条件和设备状态，自动优化采掘参数</li>
                        <li><strong>生产过程可视化：</strong>直观展示采掘过程，实现远程监控和管理</li>
                        <li><strong>产量质量预测：</strong>基于历史数据和当前状态，预测产量和质量指标</li>
                    </ul>
                    
                    <h2 style="margin-top: 2.5rem; margin-bottom: 1.5rem;">数字孪生技术在煤矿应用的实施路径</h2>
                    
                    <p>煤矿企业要成功应用数字孪生技术，需要遵循科学的实施路径，避免盲目投入和技术陷阱。基于我参与的多个煤矿数字孪生项目经验，建议采取以下实施策略：</p>
                    
                    <h3 style="margin-top: 2rem; margin-bottom: 1rem;">1. 分阶段实施策略</h3>
                    <ul style="padding-left: 1.5rem; margin-bottom: 1.5rem;">
                        <li><strong>基础夯实阶段：</strong>完善基础设施建设，包括通信网络、传感器布设、数据采集系统等</li>
                        <li><strong>单元试点阶段：</strong>选择价值高、风险小的应用场景进行试点，如通风系统、关键设备管理等</li>
                        <li><strong>系统集成阶段：</strong>将试点成果逐步扩展，实现各子系统的集成与协同</li>
                        <li><strong>全面应用阶段：</strong>构建完整的煤矿数字孪生体系，实现全矿井的智能化管理</li>
                    </ul>
                    
                    <h3 style="margin-top: 2rem; margin-bottom: 1rem;">2. 关键技术突破</h3>
                    <ul style="padding-left: 1.5rem; margin-bottom: 1.5rem;">
                        <li><strong>多源异构数据融合：</strong>解决来自不同系统、不同格式的数据融合问题</li>
                        <li><strong>高精度三维建模：</strong>提高矿井空间结构和设备的建模精度和效率</li>
                        <li><strong>实时数据处理：</strong>提升海量数据的实时处理能力，满足毫秒级响应需求</li>
                        <li><strong>智能算法优化：</strong>开发适应矿井复杂环境的智能分析和预测算法</li>
                        <li><strong>人机交互优化：</strong>提升系统易用性，降低操作人员使用门槛</li>
                    </ul>
                    
                    <h3 style="margin-top: 2rem; margin-bottom: 1rem;">3. 组织与人才保障</h3>
                    <ul style="padding-left: 1.5rem; margin-bottom: 1.5rem;">
                        <li><strong>跨部门协作机制：</strong>建立信息、生产、安全等部门的协作机制，打破信息壁垒</li>
                        <li><strong>专业人才培养：</strong>培养既懂煤矿业务又熟悉数字技术的复合型人才</li>
                        <li><strong>管理流程再造：</strong>基于数字孪生系统，优化调整管理流程和决策机制</li>
                        <li><strong>持续改进机制：</strong>建立系统运行评估和持续改进机制，不断提升应用效果</li>
                    </ul>
                    
                    <h2 style="margin-top: 2.5rem; margin-bottom: 1.5rem;">数字孪生技术的未来发展趋势</h2>
                    
                    <p>随着技术的不断进步和应用实践的深入，煤矿数字孪生技术将呈现以下发展趋势：</p>
                    
                    <ul style="padding-left: 1.5rem; margin-bottom: 1.5rem;">
                        <li><strong>与5G/6G网络的深度融合：</strong>5G/6G网络的大带宽、低时延特性，将为数字孪生系统提供更可靠的通信保障，支持更复杂的实时交互和控制。</li>
                        <li><strong>AI技术的广泛应用：</strong>人工智能算法尤其是深度学习技术将在数据分析、模式识别、预测决策等方面发挥更大作用，提升系统的智能水平。</li>
                        <li><strong>边缘计算赋能：</strong>边缘计算技术将使数据处理前移，解决井下复杂环境下的网络延迟和带宽限制问题，实现更实时的智能分析和控制。</li>
                        <li><strong>跨矿区协同管理：</strong>数字孪生技术将突破单矿井的范围限制，实现多矿井、矿区乃至企业级的协同管理，提升资源配置效率。</li>
                        <li><strong>生态系统共建共享：</strong>煤矿数字孪生将从封闭走向开放，通过标准接口和开放平台，促进矿业、信息技术、装备制造等多领域的合作创新。</li>
                    </ul>
                    
                    <p>数字孪生技术在煤矿领域的应用，不仅是技术升级，更是矿业生产方式和管理模式的深刻变革。随着实践的深入和技术的成熟，数字孪生将成为煤矿智能化建设的必由之路，为煤矿安全高效绿色开采提供有力支撑。</p>
                </div>
            </div>
        </div>
    </section>
    
    <!-- 页脚 -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-logo">
                    <h3 style="color: var(--white);">张<span>洁贞</span></h3>
                    <p>矿业信息化解决方案专家</p>
                    <div class="social-icons">
                        <a href="#" class="social-icon" title="抖音">
                            <img src="../assets/images/svg/douyin.svg" alt="抖音" style="width: 16px; height: 16px;">
                        </a>
                        <a href="#" class="social-icon" title="小红书">
                            <img src="../assets/images/svg/xiaohongshu.svg" alt="小红书" style="width: 16px; height: 16px;">
                        </a>
                        <a href="#" class="social-icon" title="微信">
                            <img src="../assets/images/svg/weixin.svg" alt="微信" style="width: 16px; height: 16px;">
                        </a>
                        <a href="#" class="social-icon" title="博客">
                            <img src="../assets/images/svg/boke.svg" alt="博客" style="width: 16px; height: 16px;">
                        </a>
                    </div>
                </div>
                
                <div class="footer-links">
                    <h4>网站导航</h4>
                    <ul>
                        <li><a href="../index.html">首页</a></li>
                        <li><a href="../about.html">关于我</a></li>
                        <li><a href="../blog/index.html">博客</a></li>
                        <li><a href="../case-studies/index.html">案例</a></li>
                        <li><a href="../contact.html">联系</a></li>
                    </ul>
                </div>
                
                <div class="footer-links">
                    <h4>联系方式</h4>
                    <ul>
                        <li>
                            <img src="../assets/images/svg/email.svg" alt="邮箱" style="width: 16px; height: 16px; margin-right: 8px; vertical-align: middle;">
                            <a href="mailto:<EMAIL>"><EMAIL></a>
                        </li>
                        <li>
                            <img src="../assets/images/svg/dianhua.svg" alt="电话" style="width: 16px; height: 16px; margin-right: 8px; vertical-align: middle;">
                            <a href="tel:+8613938155869">+86 139 3815 5869</a>
                        </li>
                        <li>
                            <img src="../assets/images/svg/weixin.svg" alt="微信" style="width: 16px; height: 16px; margin-right: 8px; vertical-align: middle;">
                            <span style="color: var(--light-gray);">zhangjiezhen176</span>
                        </li>
                    </ul>
                </div>
            </div>
            
            <div class="footer-bottom">
                <p>&copy; 2025 张洁贞 - 矿业信息化解决方案专家.</p>
            </div>
        </div>
    </footer>
    
    <!-- JavaScript -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 移动导航菜单
            const mobileNavHTML = `
                <div class="mobile-nav">
                    <button class="close-mobile-nav">
                        <i class="fas fa-times"></i>
                    </button>
                    <ul>
                        <li><a href="../index.html">首页</a></li>
                        <li><a href="../about.html">关于我</a></li>
                        <li><a href="../blog/index.html" class="active">博客</a></li>
                        <li><a href="../case-studies/index.html">案例</a></li>
                        <li><a href="../contact.html">联系</a></li>
                    </ul>
                </div>
                <div class="mobile-nav-backdrop"></div>
            `;
            
            // 将移动导航菜单添加到body
            document.body.insertAdjacentHTML('beforeend', mobileNavHTML);
            
            // 移动导航菜单事件处理
            const header = document.querySelector('.header');
            const scrollThreshold = 50;
            const mobileNavToggle = document.querySelector('.mobile-nav-toggle');
            const mobileNav = document.querySelector('.mobile-nav');
            const closeBtn = document.querySelector('.close-mobile-nav');
            const backdrop = document.querySelector('.mobile-nav-backdrop');
            
            function handleScroll() {
                if (window.scrollY > scrollThreshold) {
                    header.classList.add('scrolled');
                } else {
                    header.classList.remove('scrolled');
                }
            }
            
            window.addEventListener('scroll', handleScroll);
            // 初始化滚动事件处理
            handleScroll();
            
            // 移动导航菜单事件处理
            mobileNavToggle.addEventListener('click', function() {
                mobileNav.classList.add('active');
                backdrop.classList.add('active');
                document.body.style.overflow = 'hidden';
            });
            
            function closeNav() {
                mobileNav.classList.remove('active');
                backdrop.classList.remove('active');
                document.body.style.overflow = '';
            }
            
            closeBtn.addEventListener('click', closeNav);
            backdrop.addEventListener('click', closeNav);
            
            // 侧边栏导航菜单事件处理
            const currentPath = window.location.pathname;
            const navLinks = document.querySelectorAll('.navbar a');
            const mobileNavLinks = document.querySelectorAll('.mobile-nav a');
            
            navLinks.forEach(link => {
                const href = link.getAttribute('href');
                if (href.includes('/blog/') && currentPath.includes('/blog/')) {
                    link.classList.add('active');
                }
            });
            
            mobileNavLinks.forEach(link => {
                const href = link.getAttribute('href');
                if (href.includes('/blog/') && currentPath.includes('/blog/')) {
                    link.classList.add('active');
                }
            });
        });
    </script>
</body>
</html>
