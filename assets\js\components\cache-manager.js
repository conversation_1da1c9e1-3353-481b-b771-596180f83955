/**
 * 本地缓存管理器
 * 优化中国网络环境下的资源加载，减少不必要的网络请求
 */
const CacheManager = {
    /**
     * 初始化缓存管理器
     * @param {Object} options - 配置选项
     */
    init(options = {}) {
        // 默认配置
        this.config = {
            prefix: 'zjz_cache_',
            defaultExpiration: 60 * 24, // 默认24小时（分钟单位）
            memoryCache: true,
            ...options
        };
        
        // 内存缓存对象
        this.memoryCache = {};
        
        // 清理过期缓存
        this.cleanExpiredCache();
        
        return this;
    },
    
    /**
     * 设置缓存数据
     * @param {string} key - 缓存键
     * @param {*} data - 要缓存的数据
     * @param {number} expirationMinutes - 过期时间（分钟）
     * @returns {boolean} - 是否成功设置
     */
    set(key, data, expirationMinutes) {
        try {
            const cacheKey = this.config.prefix + key;
            const expiration = expirationMinutes || this.config.defaultExpiration;
            const now = new Date().getTime();
            
            // 缓存项
            const item = {
                data: data,
                expire: now + (expiration * 60 * 1000) // 转换为毫秒
            };
            
            // 存储到本地存储
            localStorage.setItem(cacheKey, JSON.stringify(item));
            
            // 同时存储到内存缓存中
            if (this.config.memoryCache) {
                this.memoryCache[cacheKey] = item;
            }
            
            return true;
        } catch (error) {
            console.error('缓存设置失败:', error);
            return false;
        }
    },
    
    /**
     * 获取缓存数据
     * @param {string} key - 缓存键
     * @returns {*} - 缓存的数据或null
     */
    get(key) {
        try {
            const cacheKey = this.config.prefix + key;
            const now = new Date().getTime();
            
            // 首先尝试从内存缓存获取
            if (this.config.memoryCache && this.memoryCache[cacheKey]) {
                const item = this.memoryCache[cacheKey];
                
                if (now < item.expire) {
                    return item.data;
                } else {
                    // 已过期，从内存中移除
                    delete this.memoryCache[cacheKey];
                }
            }
            
            // 从本地存储获取
            const itemStr = localStorage.getItem(cacheKey);
            if (!itemStr) return null;
            
            const item = JSON.parse(itemStr);
            
            // 检查是否过期
            if (now > item.expire) {
                localStorage.removeItem(cacheKey);
                return null;
            }
            
            // 更新到内存缓存
            if (this.config.memoryCache) {
                this.memoryCache[cacheKey] = item;
            }
            
            return item.data;
        } catch (error) {
            console.error('缓存获取失败:', error);
            return null;
        }
    },
    
    /**
     * 移除特定缓存
     * @param {string} key - 缓存键
     */
    remove(key) {
        const cacheKey = this.config.prefix + key;
        
        // 从本地存储移除
        localStorage.removeItem(cacheKey);
        
        // 从内存缓存移除
        if (this.config.memoryCache) {
            delete this.memoryCache[cacheKey];
        }
    },
    
    /**
     * 清理所有缓存
     */
    clear() {
        // 清理所有带前缀的本地存储项
        for (let i = 0; i < localStorage.length; i++) {
            const key = localStorage.key(i);
            if (key && key.startsWith(this.config.prefix)) {
                localStorage.removeItem(key);
            }
        }
        
        // 清理内存缓存
        if (this.config.memoryCache) {
            this.memoryCache = {};
        }
    },
    
    /**
     * 清理过期缓存
     */
    cleanExpiredCache() {
        const now = new Date().getTime();
        
        // 清理本地存储中的过期项
        for (let i = 0; i < localStorage.length; i++) {
            const key = localStorage.key(i);
            
            if (key && key.startsWith(this.config.prefix)) {
                try {
                    const itemStr = localStorage.getItem(key);
                    if (itemStr) {
                        const item = JSON.parse(itemStr);
                        
                        if (now > item.expire) {
                            localStorage.removeItem(key);
                            
                            // 同时从内存缓存移除
                            if (this.config.memoryCache) {
                                delete this.memoryCache[key];
                            }
                        }
                    }
                } catch (error) {
                    // 解析错误，移除该项
                    localStorage.removeItem(key);
                }
            }
        }
    },
    
    /**
     * 计算缓存使用情况
     * @returns {Object} 缓存统计信息
     */
    getStats() {
        let size = 0;
        let count = 0;
        
        for (let i = 0; i < localStorage.length; i++) {
            const key = localStorage.key(i);
            
            if (key && key.startsWith(this.config.prefix)) {
                const value = localStorage.getItem(key);
                size += (key.length + value.length) * 2; // UTF-16编码每个字符2字节
                count++;
            }
        }
        
        return {
            count: count,
            size: size,
            sizeInKB: (size / 1024).toFixed(2)
        };
    },
    
    /**
     * 使用缓存加载JSON
     * @param {string} url - 请求URL
     * @param {Object} options - 请求选项
     * @returns {Promise} - Promise对象
     */
    async fetchJSON(url, options = {}) {
        // 生成缓存键
        const cacheKey = 'fetch_' + url;
        
        // 检查缓存
        const cachedData = this.get(cacheKey);
        if (cachedData) {
            return Promise.resolve(cachedData);
        }
        
        // 配置选项
        const fetchOptions = {
            method: 'GET',
            // 中国特定的请求头，避免某些ISP缓存问题
            headers: {
                'Cache-Control': 'no-cache',
                'Pragma': 'no-cache',
                'X-Requested-With': 'XMLHttpRequest'
            },
            ...options
        };
        
        try {
            const response = await fetch(url, fetchOptions);
            
            if (!response.ok) {
                throw new Error(`请求失败: ${response.status}`);
            }
            
            const data = await response.json();
            
            // 缓存响应数据
            this.set(cacheKey, data, options.expiration);
            
            return data;
        } catch (error) {
            console.error('请求失败:', error);
            throw error;
        }
    }
};

// 自动初始化
document.addEventListener('DOMContentLoaded', () => {
    CacheManager.init();
    
    // 暴露给全局，便于其他脚本使用
    window.CacheManager = CacheManager;
}); 