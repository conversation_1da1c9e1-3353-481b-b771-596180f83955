/**
 * 加载管理器 - 控制页面资源加载过程与显示反馈
 * 适用于中国网络环境，优化初始加载体验
 */
const LoadingManager = {
    /**
     * 初始化加载管理器
     * @param {Object} options - 配置选项
     */
    init(options = {}) {
        // 默认配置
        this.config = {
            showProgress: true,
            minLoadTime: 800,
            maxLoadTime: 8000,
            ...options
        };
        
        // 初始化进度条
        if (this.config.showProgress && typeof NProgress !== 'undefined') {
            NProgress.configure({ 
                minimum: 0.15,
                easing: 'ease', 
                speed: 500,
                showSpinner: false
            });
            NProgress.start();
        }
        
        // 记录开始时间
        this.startTime = Date.now();
        
        // 监听页面加载完成事件
        window.addEventListener('load', () => this.onPageLoaded());
        
        // 设置最大加载时间
        this.maxLoadTimer = setTimeout(() => this.onPageLoaded(), this.config.maxLoadTime);
        
        // 应用骨架屏
        this.initSkeletonScreen();
        
        return this;
    },
    
    /**
     * 初始化骨架屏
     */
    initSkeletonScreen() {
        const contentElements = document.querySelectorAll('[data-skeleton]');
        contentElements.forEach(element => {
            // 隐藏实际内容
            element.style.visibility = 'hidden';
            
            // 创建骨架屏
            const skeleton = document.createElement('div');
            skeleton.className = 'content-skeleton';
            
            // 根据内容类型创建不同的骨架形状
            const type = element.getAttribute('data-skeleton');
            
            // 根据类型添加不同的骨架结构
            switch(type) {
                case 'text':
                    skeleton.innerHTML = `
                        <div class="skeleton" style="width: 90%;"></div>
                        <div class="skeleton" style="width: 60%;"></div>
                        <div class="skeleton" style="width: 75%;"></div>
                    `;
                    break;
                case 'image':
                    skeleton.innerHTML = `<div class="skeleton-image"></div>`;
                    break;
                case 'card':
                    skeleton.innerHTML = `
                        <div class="skeleton-image"></div>
                        <div class="skeleton" style="width: 90%; margin-top: 12px;"></div>
                        <div class="skeleton" style="width: 60%;"></div>
                    `;
                    break;
                default:
                    skeleton.innerHTML = `<div class="skeleton"></div>`;
            }
            
            // 插入骨架屏
            element.parentNode.insertBefore(skeleton, element);
            
            // 加入到需要恢复的元素列表中
            this.skeletonElements = this.skeletonElements || [];
            this.skeletonElements.push({
                content: element,
                skeleton: skeleton
            });
        });
    },
    
    /**
     * 隐藏骨架屏，显示实际内容
     */
    hideSkeletons() {
        if (this.skeletonElements) {
            this.skeletonElements.forEach(({content, skeleton}, index) => {
                // 设置延迟，使内容显示更平滑
                setTimeout(() => {
                    // 显示实际内容
                    content.style.visibility = 'visible';
                    content.style.opacity = 0;
                    content.style.transform = 'translateY(10px)';
                    
                    // 添加淡入动画
                    content.style.transition = 'opacity 0.3s ease, transform 0.3s ease';
                    
                    // 触发回流并应用动画
                    setTimeout(() => {
                        content.style.opacity = 1;
                        content.style.transform = 'translateY(0)';
                        
                        // 移除骨架屏
                        skeleton.style.opacity = 0;
                        setTimeout(() => {
                            skeleton.parentNode.removeChild(skeleton);
                        }, 300);
                    }, 30);
                }, index * 100); // 内容出现的间隔时间
            });
        }
    },
    
    /**
     * 页面加载完成的处理
     */
    onPageLoaded() {
        // 清除超时计时器
        clearTimeout(this.maxLoadTimer);
        
        // 计算加载耗时
        const loadTime = Date.now() - this.startTime;
        
        // 确保至少显示最小时间，给用户良好的视觉反馈
        const remainingTime = Math.max(0, this.config.minLoadTime - loadTime);
        
        setTimeout(() => {
            // 隐藏进度条
            if (this.config.showProgress && typeof NProgress !== 'undefined') {
                NProgress.done();
            }
            
            // 隐藏骨架屏
            this.hideSkeletons();
            
            // 触发自定义事件，通知页面已完全加载
            document.dispatchEvent(new CustomEvent('page:fully-loaded'));
            
            // 发送页面加载时间统计
            this.sendLoadingMetrics(loadTime);
        }, remainingTime);
    },
    
    /**
     * 发送页面加载性能指标
     * @param {number} loadTime - 加载时间(ms)
     */
    sendLoadingMetrics(loadTime) {
        // 收集性能指标
        if (window.performance && window.performance.timing) {
            const timing = window.performance.timing;
            const navigationStart = timing.navigationStart;
            
            // 关键指标
            const metrics = {
                // 首次内容绘制
                FCP: window.performance.getEntriesByType('paint')
                    .find(entry => entry.name === 'first-contentful-paint')?.startTime || 0,
                // DNS查询时间 (中国网络环境下可能较长)
                DNS: timing.domainLookupEnd - timing.domainLookupStart,
                // 连接时间
                Connect: timing.connectEnd - timing.connectStart,
                // DOM解析时间
                DOMParse: timing.domInteractive - timing.responseEnd,
                // 总加载时间
                TotalLoad: loadTime
            };
            
            // 此处可集成发送到统计服务器的代码
            console.info('页面加载性能指标:', metrics);
        }
    }
};

// 自动初始化
document.addEventListener('DOMContentLoaded', () => {
    // 只有非预加载页面才使用加载管理器
    if (!document.body.classList.contains('preloaded')) {
        LoadingManager.init();
    }
}); 